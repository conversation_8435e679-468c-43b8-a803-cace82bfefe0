# La Ruche landing page

## Table of Contents
- [Overview](#overview)
- [<PERSON> Stack](#tech-stack)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Environment Variables](#environment-variables)
- [Running the Project](#running-the-project)
- [File Structure](#file-structure)
- [Routing](#routing)
- [Styling](#styling)
- [Internationalization](#internationalization)
- [Deployment](#deployment)
- [Coding Guidelines](#coding-guidelines)
- [Contributing](#contributing)

---

## Overview

This is a **Remix** project that utilizes **server-side rendering (SSR)**. 

## Tech Stack

- **Framework:** [Remix](https://remix.run/)
- **Language:** TypeScript
- **UI Library:** React with <PERSON><PERSON>
- **Styling:** SCSS Modules
- **State Management:** React's built-in state
- **Internationalization:** remix-i18next
- **Deployment:** Vercel

## Project Structure
```
/project-root
├── app/
│   ├── components/   # Reusable UI components
│   ├── routes/       # Remix route files
│   ├── styles/       # Global & utility styles
│   ├── utils/        # Helper functions
│   ├── constants/      # Static values
│   ├── data/      # Dynamic content that may change
│   ├── hooks/        # Custom hooks
│   ├── localization/      # Setup and translations with i18n
│   ├── types/      # Typescript interfaces
│   ├── entry.client.tsx  # Client entry point
│   ├── entry.server.tsx  # Server entry point
│   ├── root.tsx     # Root layout
├── public/          # Static assets
├── .env
├── vite.config.ts  # Remix & Vite configuration
├── package.json 
```

## Installation
Clone the repository and install dependencies:
```sh
$ git clone https://github.com/LaRucheHealth/landing-page.git
$ cd landing-page
$ npm install
```

## Environment Variables

- Create a `.env` file and configure required variables from `/example.env`:
- Also create a `.env.production.local` file with the variables from `/example.env`

## Running the Project
Start the development server:
```sh
$ npm run dev
```
For a production preview:
```sh
$ npm run preview
```

## File Structure

### Routes

- **`app/routes/($lang)._index/route.tsx`** - Homepage
- **`app/routes/($lang).chatbot/route.tsx`** - Chatbot page

## Styling

- Uses **SCSS Modules** for scoped styles.
- Prefer **khebab-case** over camelCase.
- Styles should be localized to pages or components.
- **Global styles** are in `app/styles/global.scss`.

## Internationalization

This project uses `remix-i18next` for translations. Locale files are in `app/localization/locales`.
```tsx
import { useTranslation } from '~/hooks/index.ts';
// NOTE: Import useTranslation from a custom hook not remix-i18next!
const { t } = useTranslation();
return <h1>{t('welcome')}</h1>;
```

## Deployment

This project is deployed on **Vercel**.


## Coding Guidelines

<!-- - Follow the **Airbnb style guide** for TypeScript. -->
- Lint with `npm run lint`.
- Use **ESLint & Prettier** for formatting.
- Use descriptive commit messages.
- All modules should be re-exported in their respective `index.ts` files for clean imports.

## Contributing

1. Fork the repo & create a feature branch.
2. Make changes & commit with a descriptive message.
3. Submit a pull request.

