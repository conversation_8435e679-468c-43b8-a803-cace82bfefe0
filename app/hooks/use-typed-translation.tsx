import { TOptionsBase } from "i18next";
import { $Dictionary } from "node_modules/i18next/typescript/helpers";
import { useTranslation as _useTranslation } from "react-i18next";
import { translationsEn } from "~/localization";

// Custom hook for type-safe strings with useTranslation
// Source: Discussion on remix-i18next by @Cmoen11
// https://github.com/sergiodxa/remix-i18next/discussions/179#discussion-6287074
export type KeysUnion<T, Cache extends string = ""> = T extends PropertyKey
  ? Cache
  : {
      [P in keyof T]: P extends string
        ? Cache extends ""
          ? KeysUnion<T[P], `${P}`>
          : Cache | KeysUnion<T[P], `${Cache}.${P}`>
        : never;
    }[keyof T];

export type TranslationKey = KeysUnion<typeof translationsEn>;

/**
 * A type-safe wrapper around react-i18next's useTranslation hook.
 *
 * @returns The same object as useTranslation, but with the `t` function
 *          having a type-safe signature. The `t` function takes a translation
 *          string key as its argument, and the type of that key is inferred
 *          from the translationsEN object.
 */
export const useTranslation = () => {
  const translation = _useTranslation();
  const t = (
    str: KeysUnion<typeof translationsEn>,
    options?: (TOptionsBase & $Dictionary) | undefined
  ): string => translation.t(str, options) as string;

  return { ...translation, t };
};
