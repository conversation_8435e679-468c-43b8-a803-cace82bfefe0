import "@mantine/core/styles.css";
import "@mantine/carousel/styles.css";
import "@mantine/spotlight/styles.css";
import "./styles/global.scss";

import {
  <PERSON>ton,
  Center,
  ColorSchemeScript,
  createTheme,
  em,
  MantineColorsTuple,
  <PERSON>tineProvider,
  <PERSON>ack,
  Textarea,
  TextInput,
} from "@mantine/core";
import { json, LoaderFunctionArgs } from "@remix-run/node";
import {
  isRouteErrorResponse,
  Link,
  Links,
  Meta,
  Outlet,
  redirect,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  useRouteError,
} from "@remix-run/react";
import { useChangeLanguage } from "remix-i18next/react";

import { useTranslation } from "~/hooks/use-typed-translation";
import { localeCookie } from "~/localization/i18n.server";

import i18n from "./localization/i18n";
import { useDocumentTitle } from "@mantine/hooks";
import { Footer, Navbar } from "./components";

// From logo color
const primary: MantineColorsTuple = [
  "#e2fdfd",
  "#d4f6f4",
  "#afe8e7",
  "#88dad9",
  "#66cfcd",
  "#4fc7c5",
  "#40c4c2",
  "#2dadab",
  "#199a98",
  "#008684",
];

// const accent: MantineColorsTuple = [
//   "#f0eaff",
//   "#dacfff",
//   "#b19cff",
//   "#8664ff",
//   "#6136ff",
//   "#4a18ff",
//   "#3d07ff",
//   "#2f00e5",
//   "#2800cd",
//   "#1c00b5",
// ];
const accent: MantineColorsTuple = [
  "#EDE8FF",
  "#C7B7FF",
  "#AC95FF",
  "#8664FF",
  "#6E46FF",
  "#4A18FF",
  "#4316E8",
  "#3511B5",
  "#290D8C",
  "#1F0A6B",
];

const themeOverride = createTheme({
  breakpoints: {
    xs: em(414),
    sm: em(768),
    md: em(1024),
    lg1: em(1200),
    lg2: em(1440),
    lg3: em(1600),
    lg4: em(1920),
  },
  primaryColor: "accent",
  black: "hsl(242, 98%, 16%)",
  colors: {
    primary,
    accent,
  },
  fontFamily: "Poppins, sans-serif",
  components: {
    TextInput: TextInput.extend({
      defaultProps: { radius: "sm" },
      classNames: {
        input: "mantine-text-input",
        root: "mantine-text-input-wrapper",
      },
    }),
    TextArea: Textarea.extend({
      defaultProps: { radius: "sm" },
    }),
    Button: Button.extend({
      defaultProps: {
        radius: "xl",
        color: "accent",
      },
      classNames: {
        root: "mantine-button",
      },
    }),
  },
});

export async function loader({ request, params }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const langOrSlug = params.lang as string | undefined;
  const isSlug = langOrSlug && langOrSlug.length > 2;

  let locale: string | undefined;

  if (!isSlug && langOrSlug && i18n.supportedLngs.includes(langOrSlug)) {
    // If `langOrSlug` is a valid 2-character language code, use it as locale
    locale = langOrSlug;
  } else {
    // If it's a slug (or invalid language), check the cookie for a valid locale
    const cookieHeader = request.headers.get("Cookie");
    const cookieLang = await localeCookie.parse(cookieHeader);

    if (cookieLang && i18n.supportedLngs.includes(cookieLang)) {
      locale = cookieLang;
    } else {
      // Fallback to the default language if neither URL nor cookie have a valid locale
      locale = i18n.fallbackLng;
    }
  }

  // If we have a valid locale but not in the URL, redirect to the correct URL with locale and slug if present
  if (!langOrSlug || isSlug) {
    url.pathname = `/${locale}${
      isSlug ? `/${langOrSlug}` : ""
    }${url.pathname.replace(/^\/[^/]+/, "")}`;
    return redirect(url.toString(), {
      headers: { "Set-Cookie": await localeCookie.serialize(locale) },
    });
  }

  // console.log("root:", langOrSlug);

  // Return locale and slug in response and set the cookie if we reached here without needing to redirect
  return json(
    { locale },
    { headers: { "Set-Cookie": await localeCookie.serialize(locale) } }
  );
}

export function Layout({ children }: { children: React.ReactNode }) {
  // Get the locale from the loader
  // const { locale } = useLoaderData<typeof loader>();
  const data = useLoaderData<typeof loader>();

  const { t, i18n } = useTranslation();

  // This hook will change the i18n instance language to the current locale
  // detected by the loader, this way, when we do something to change the
  // language, this locale will change and i18next will load the correct
  // translation files
  useChangeLanguage(data?.locale as string);

  return (
    <html lang={data?.locale} dir={i18n.dir()}>
      <head>
        {/* Clarity analytics tracking */}
        <script
          type="text/javascript"
          dangerouslySetInnerHTML={{
            __html: `
              if (window.location.host.includes("laruche.health")) {
                // Microsoft Clarity
                (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "rfdqiu472d");

                // Facebook Pixel
                !(function(f,b,e,v,n,t,s){
                  if(f.fbq)return;n=f.fbq=function(){
                    n.callMethod?
                    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                  if(!f._fbq)f._fbq=n;
                  n.push=n;n.loaded=!0;n.version='2.0';
                  n.queue=[];t=b.createElement(e);t.async=!0;
                  t.src=v;s=b.getElementsByTagName(e)[0];
                  s.parentNode.insertBefore(t,s)
                })(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');

                fbq('init', '3866583020274251');
                fbq('track', 'PageView');
              }
            `,
          }}
        />
        <noscript>
          <img
            height="1"
            width="1"
            style={{ display: "none" }}
            src="https://www.facebook.com/tr?id=3866583020274251&ev=PageView&noscript=1"
          />
        </noscript>

        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {/* <meta name="description" content={t("meta-description")} /> */}
        {/* <meta property="og:description" content={t("meta-description")} /> */}
        <Meta />
        <ColorSchemeScript />

        <link rel="icon" type="image/svg+xml" href="/logo-icon.svg" />
        {/* <!-- Google font --> */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Mono:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap&subset=latin,latin-ext"
          rel="stylesheet"
        />
        <Links />
      </head>
      <body>
        <MantineProvider theme={themeOverride}>
          <Navbar />
          {children}
          <Footer />
        </MantineProvider>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}

export function ErrorBoundary() {
  const error = useRouteError();

  useDocumentTitle("An error may have occurred");

  if (isRouteErrorResponse(error)) {
    const errorData = error.data;

    return (
      <main>
        <Center mt={"5rem"}>
          <Stack>
            <p>
              {error.data?.message ||
                "Something went wrong. Please try again later"}
            </p>
            {errorData.details && <p>Details: {errorData.details}</p>}
            <Button w={"fit-content"} mx="auto" component={Link} to="/">
              Back to home
            </Button>
          </Stack>
        </Center>
      </main>
    );
  }
}
