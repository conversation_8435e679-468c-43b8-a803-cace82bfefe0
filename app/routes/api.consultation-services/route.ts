import { json } from "@remix-run/node";
import { isAxiosError } from "axios";

import { ComboboxItemPrice } from "~/types";
import { api } from "~/utils/api.server";

export const loader = async () => {
  // return json({ error: "Error fetching data" }, { status: 404 });
  try {
    const res = await api.get("clinic/consultation-services", {
      params: { status: "active" },
    });

    let data: ComboboxItemPrice[] = [];

    // console.log(res.data);

    if (res?.data.length) {
      data = res?.data.map(
        ({ name, _id, price, service_duration, category }: any) => ({
          value: _id,
          label: name,
          price,
          duration: service_duration ?? 0,
          category,
        })
      );
    }

    return json({
      ok: true,
      data,
      error: null,
    });
  } catch (error) {
    console.error("[Error fetching consultation services]", error);

    // return json(
    //   {
    //     ok: false,
    //     data: null,
    //     error: {
    //       message: "There was an error getting consultation services",
    //       status: 500,
    //     },
    //   },
    //   { status: 500 }
    // );

    // Check if it's a network error (no response received)
    if (isAxiosError(error)) {
      if (error.response) {
        // Axios response error (e.g., 404, 500)
        return json(
          {
            ok: false,
            data: null,
            error:
              error.response.data?.detail ||
              "Error fetching consultation services",
          },
          { status: error.response.status }
        );
      } else if (error.request) {
        // Network error (request was made, but no response)
        return json(
          {
            ok: false,
            data: null,
            error:
              "Network error: No response from server. Please try again later.",
          },
          { status: 503 } // 503 Service Unavailable or 504 Gateway Timeout
        );
      }
    }

    // General error handler for non-Axios errors
    return json(
      {
        ok: false,
        data: null,
        error: "There was an error getting consultation services",
      },
      { status: 500 }
    );
  }
};
