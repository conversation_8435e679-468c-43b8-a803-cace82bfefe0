import { j<PERSON>, <PERSON>aFunction } from "@remix-run/node";
import { JoinOurTeam } from "./sections/join-our-team";
import { Statistics } from "./sections/statistics";
import { Services } from "./sections/services";
import { DownloadKiko } from "./sections/download-kiko";
import { Partners } from "./sections/partners";
import { TeleconsultationSpecialties } from "./sections/teleconsultation-specialties";
import { Hero } from "./sections/hero";
import { loader as consultationServicesLoaderFunc } from "~/routes/api.consultation-services/route";
import { loader as doctorsLoaderFunc } from "~/routes/api.doctor-profiles/route";
import { NewHero } from "./sections/new-hero";
import { GetTheApp } from "./sections/get-the-app";
import { LoaderFunction } from "@remix-run/node";
import { TranslationKey } from "~/hooks";
import { HomecareSpecialties } from "./sections/homecare-specialties";
import { ComboboxItemPrice, DoctorProfile } from "~/types";
import { KikoChat } from "./modules/kiko-chat";


export const meta: MetaFunction = () => {
  return [
    { title: "La Ruche Health" },
    {
      name: "description",
      content: "Connect to quality healthcare in minutes!",
    },
  ];
};


// todo: Replace all mantine components with html and use Scss to style

export default function Route() {
  return (
    <>
      <NewHero />
      <Partners />
      <GetTheApp />
      {/* <Hero /> */}
      {/* <Services /> */}
      <TeleconsultationSpecialties />
      <HomecareSpecialties />
      <DownloadKiko />
      <Statistics />
      <JoinOurTeam />
      <KikoChat />
    </>
  );
}

export const loader: LoaderFunction = async ({ request, params, ...rest }) => {
  // const t = await i18nextServer.getFixedT(request, "translation", {
  //   lng: "fr",
  // });
  const lang = params.lang as string | undefined;
  // console.log("book appointment locale:", locale);

  // * NB: Translation doesn't work for meta tags after deployment

  // const title = `${t("book-appointment-now")} • ${PAGE_TITLE}`;
  try {
    const [
      profilesRes,
      consultationServicesRes,
      // specialtiesRes,
    ] = await Promise.all([
      doctorsLoaderFunc({ request, params, ...rest }),
      consultationServicesLoaderFunc(),
      // specialtiesLoaderFunc(),
    ]);

    const data1 = await profilesRes.json();
    const data2 = await consultationServicesRes.json();
    const data3 = {
      ok: true,
      error: null,
      data: data1?.data
        ? data1.data.reduce((specialties: string[], doctor: DoctorProfile) => {
          if (doctor?.specialties) {
            doctor.specialties.forEach(specialty =>
              specialties.push(specialty)
            );
          }
          return specialties;
        }, [] as string[])
        : [],
    };

    // Construct ok object to show status for each endpoint
    const ok = {
      profiles: data1.ok,
      consultationServices: data2.ok,
      specialties: data3.ok,
    };

    // Construct error object for each failed response
    const errors: Record<string, any> = {};
    if (!data1.ok) {
      errors.profilesError = data1?.error;
    }
    if (!data2.ok) {
      errors.consultationServicesError = data2?.error;
    }
    if (!data3.ok) {
      errors.specialtiesError = data3?.error;
    }

    // Extract languages from doctor profiles
    const languages = data1?.data
      ? data1.data.reduce((languages: string[], doctor: DoctorProfile) => {
        if (doctor?.languages) {
          doctor.languages.forEach(language => {
            // Don't include English and French as they are already hard-coded into the filters
            if (
              !language.toLocaleLowerCase().startsWith("fran") &&
              !language.toLocaleLowerCase().startsWith("ang")
            ) {
              languages.push(language.toLocaleLowerCase());
            }
          });
        }
        return languages;
      }, [] as string[])
      : [];

    // Unique languages
    const uniqueLanguages = Array.from(new Set(languages));

    // console.log("languages:", uniqueLanguages);

    // If any API call failed, return combined response with individual ok statuses
    if (Object.keys(errors).length > 0) {
      return json(
        {
          ok,
          title: "Title",
          data: {
            profilesData: data1?.data || null,
            consultationServicesData: data2?.data || null,
            specialtiesData: data3?.data || null,
            languages: data1?.data ? uniqueLanguages : [],
          },
          error: errors,
        },
        { status: 500 }
      );
    }

    // Return data if both requests succeeded
    return json(
      {
        ok,
        title: "title",
        data: {
          profilesData: data1.data,
          consultationServicesData: data2.data,
          specialtiesData: data3.data,
          languages: uniqueLanguages,
        },
        error: null,
      },
      {
        headers: {
          "Cache-Control": "max-age=3600", // Cache for 1 hour
        },
      }
    );
  } catch (error) {
    // Handle any unexpected errors outside of API responses
    console.error("Unexpected error", error);
    return json(
      {
        ok: {
          profiles: false,
          consultationServices: false,
          specialties: false,
        },
        title: "title",
        data: {
          profilesData: [],
          consultationServicesData: [],
          specialtiesData: [],
          languages: [],
        },
        error: "Unexpected error occurred",
      },
      { status: 500 }
    );
  }
};

export function ErrorBoundary({ error }: { error: Error }) {
  console.error(error); // optional for logging

  return (
    <div style={{ padding: "2rem", textAlign: "center" }}>
      <h1>Oops! Something went wrong</h1>
      <p>{error?.message}</p>
    </div>
  );
}
