import { useState, useRef, useEffect, useCallback } from "react";
import classes from "./kiko-chat.module.scss";
import { AnimatePresence, motion } from "framer-motion";
import ChatBotIcon from "~/assets/icons/chatbot-icon.svg";
import CloseIcon from "~/assets/icons/close.svg";
import CopyIcon from "~/assets/icons/copy.svg";
import { useFetcher } from "@remix-run/react";
import { useTranslation } from "~/hooks";
import { IconSend, IconX } from "@tabler/icons-react";
import MarkdownRenderer from "~/components/markdown-renderer";
import {
  getChatSessionId,
  setChatSessionId,
  clearChatSessionId,
} from "~/utils/chat-session-storage";

type KikoChatResponse = {
  id: string;
  chatSessionId: string;
  kiko_response: string;
  buttons: any[];
  reply_suggestions: any[];
};

interface ChatMessage {
  id?: string;
  sender: "kiko" | "user";
  text: string;
  timestamp: string; // ISO string
}

export interface KikoLoadedChatMessage {
  id: string;
  chatSessionId: string;
  laruche_id: string;
  medium: "website" | string;
  sequence_number: string;
  text_message: string;
  timestamp: string; // ISO 8601 format
  userInputAudio: string;
  userInputImage: string;
  user_type: "Human" | string;
}

const SESSION_LENGTH_IN_MINUTES = 60;

export const KikoChat = () => {
  const { t, i18n } = useTranslation();

  const [loadingHistory, setLoadingHistory] = useState(false);

  const fetcher = useFetcher();
  // const lang = i18n.language.startsWith("fr") ? "fr" : "en";

  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [showExamples, setShowExamples] = useState(true);
  const [kikoTyping, setKikoTyping] = useState(false);
  const [sessionId, setSessionId] = useState("");
  const [chatExamples, setChatExamples] = useState<string[]>([]);
  const chatContentRef = useRef<HTMLDivElement>(null);
  const [inputValue, setInputValue] = useState("");

  // Onboarding animation state
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [onboardingDismissed, setOnboardingDismissed] = useState(false);
  const [hasLoadedHistory, setHasLoadedHistory] = useState(false);

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined'
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false;

  // Development helper for testing onboarding animation
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).testKikoOnboarding = () => {
        console.log("🧪 Testing onboarding animation");
        clearChatSessionId();
        setOnboardingDismissed(false);
        setHasLoadedHistory(true);
        setMessages([{
          sender: "kiko",
          text: t("kiko-init-message"),
          timestamp: new Date().toISOString(),
        }]);
        console.log("✅ Session cleared, onboarding should trigger in 6 seconds");
      };
    }
  }, [t]);

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      sender: "kiko",
      text: t("kiko-init-message"),
      timestamp: new Date().toISOString(),
    },
  ]);

  useEffect(() => {
    // Always update examples when language changes
    setChatExamples([t("kiko-example-1"), t("kiko-example-2")]);

    // Replace ONLY the first message if it's from Kiko
    setMessages(prev => {
      if (prev.length > 0 && prev[0].sender === "kiko") {
        const updated = [...prev];
        updated[0] = {
          ...updated[0],
          text: t("kiko-init-message"),
        };
        return updated;
      }
      return prev;
    });
  }, [i18n.language]);

  // const safeUpdateMessages = (rawMessages: KikoLoadedChatMessage[]) => {
  //   const sorted = rawMessages.sort((a, b) => Number(a.sequence_number) - Number(b.sequence_number));

  //   // Format to UI messages
  //   const formatted = sorted.map((msg) => {
  //     const timestamp = new Date().toISOString()
  //     const sender: "user" | "kiko" = msg.user_type === "Human" ? "user" : "kiko";
  //     return {
  //       id: msg.id,
  //       sender,
  //       text: msg.text_message,
  //       timestamp,
  //     };
  //   });

  //   // Filter out messages with duplicate IDs (only keep new ones)
  //   setMessages((prev) => {
  //     const existingIds = new Set(prev.map((m) => m.id).filter(Boolean)); // Only consider messages with ids
  //     const newMessages = formatted.filter((msg) => !msg.id || !existingIds.has(msg.id));
  //     return [...prev, ...newMessages];
  //   });
  // }

  const safeUpdateMessages = (rawMessages: KikoLoadedChatMessage[]) => {
    const sorted = rawMessages.sort(
      (a, b) => Number(a.sequence_number) - Number(b.sequence_number)
    );

    // Format to UI messages
    const formatted = sorted.map(msg => {
      const timestamp = new Date().toISOString();
      const sender: "user" | "kiko" =
        msg.user_type === "Human" ? "user" : "kiko";
      return {
        id: msg.id,
        sender,
        text: msg.text_message,
        timestamp,
      };
    });

    setMessages(prev => {
      return [prev[0], ...formatted];
    });
  };

  const handleClick = (message: string | null) => {
    fetcher.submit(
      {
        action: "send",
        text_message: message || inputValue,
        session_id: sessionId,
      },
      {
        method: "post",
        action: `/api/kiko-chat`,
        encType: "application/x-www-form-urlencoded",
      }
    );
  };

  const scrollToBottom = () => {
    if (chatContentRef.current) {
      chatContentRef.current.scrollTo({
        top: chatContentRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  const handleExampleClick = (text: string) => {
    setShowExamples(false);
    setTimeout(() => setKikoTyping(true), 200);
    const timestamp = new Date().toISOString();
    setMessages(prev => [...prev, { sender: "user", text, timestamp }]);
    handleClick(text);
    setTimeout(() => scrollToBottom(), 0);
  };

  const handleSend = () => {
    setShowExamples(false);
    setTimeout(() => setKikoTyping(true), 200);
    const text = inputValue.trim();
    if (!text) return;

    const timestamp = new Date().toISOString();
    setMessages(prev => [...prev, { sender: "user", text, timestamp }]);
    handleClick(null);
    setInputValue("");
    setTimeout(() => scrollToBottom(), 0);
  };

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => scrollToBottom(), 0);
    }
  }, [isOpen]);

  useEffect(() => {
    // Add more defensive checks for fetcher.data
    if (!fetcher.data) return;

    // Handle cases where fetcher.data might be a string (HTML error pages)
    if (typeof fetcher.data === "string") {
      console.error("❌ Kiko chat received non-JSON response:", fetcher.data);
      setMessages(prev => {
        return [
          ...prev,
          {
            sender: "kiko",
            text: t("kiko-sorry"),
            timestamp: new Date().toISOString(),
          },
        ];
      });
      setLoadingHistory(false);
      setKikoTyping(false);
      setShowExamples(false);
      return;
    }

    if (typeof fetcher.data !== "object") return;

    // ✅ Handle error responses
    if ("error" in fetcher.data && "source" in fetcher.data) {
      console.error("❌ Kiko chat error:", fetcher.data.error);
      // Optional: show toast or update UI
      if (fetcher.data.source === "action") {
        setMessages(prev => {
          return [
            ...prev,
            {
              sender: "kiko",
              text: t("kiko-sorry"),
              timestamp: new Date().toISOString(),
            },
          ];
        });
        setTimeout(() => scrollToBottom(), 200);
      }

      setLoadingHistory(false);
      setKikoTyping(false);
      setShowExamples(false);
      return;
    }

    // ✅ Handle success responses
    if ("source" in fetcher.data && "data" in fetcher.data) {
      if (fetcher.data.source === "action") {
        try {
          const data = fetcher.data.data as KikoChatResponse;
          console.log("Fetcher result:", fetcher.data);

          // Validate that we have the required data structure
          if (!data || typeof data !== 'object') {
            throw new Error('Invalid response data structure');
          }

          if (!sessionId && data.chatSessionId) {
            setSessionId(data.chatSessionId);
            setChatSessionId(data.chatSessionId, SESSION_LENGTH_IN_MINUTES);
          }

          const timestamp = new Date().toISOString();

          setKikoTyping(false);

          // Only add message if we have valid response text
          if (data.kiko_response && typeof data.kiko_response === 'string') {
            setMessages(prev => {
              const exists = prev.some(msg => msg.id === data.id);
              if (exists) return prev;
              return [
                ...prev,
                {
                  id: data.id,
                  sender: "kiko",
                  text: data.kiko_response,
                  timestamp,
                },
              ];
            });
          }

          if (data?.reply_suggestions?.length > 0) {
            setChatExamples(data.reply_suggestions);
            setShowExamples(true);
          }

          setTimeout(() => scrollToBottom(), 200);
        } catch (error) {
          console.error("Error processing Kiko response:", error);
          setMessages(prev => {
            return [
              ...prev,
              {
                sender: "kiko",
                text: t("kiko-sorry"),
                timestamp: new Date().toISOString(),
              },
            ];
          });
          setKikoTyping(false);
          setTimeout(() => scrollToBottom(), 200);
        }
      }

      if (fetcher.data.source === "loader") {
        try {
          const rawMessages = fetcher.data.data as KikoLoadedChatMessage[];
          console.log("Fetched chat history:", rawMessages);

          // Validate that we have an array of messages
          if (Array.isArray(rawMessages)) {
            safeUpdateMessages(rawMessages);
          } else {
            console.warn("Invalid chat history format received");
          }

          setLoadingHistory(false);
          setHasLoadedHistory(true);
          setKikoTyping(false);
          setShowExamples(false);
          setTimeout(() => scrollToBottom(), 200);
        } catch (error) {
          console.error("Error processing chat history:", error);
          setLoadingHistory(false);
          setHasLoadedHistory(true);
          setKikoTyping(false);
          setShowExamples(false);
        }
      }
    }
  }, [fetcher.data, sessionId]);

  useEffect(() => {
    const storedChatSessionId = getChatSessionId();

    if (storedChatSessionId) {
      setLoadingHistory(true);
      setChatSessionId(storedChatSessionId);
      fetcher.load(`/api/kiko-chat?session_id=${storedChatSessionId}`);
    } else {
      setHasLoadedHistory(true);
    }
  }, []);

  // Onboarding animation logic
  useEffect(() => {
    // Check if user should see onboarding animation
    const shouldShowOnboarding = () => {
      // Don't show if already dismissed
      if (onboardingDismissed) {
        console.log("❌ Onboarding dismissed");
        return false;
      }

      // Don't show if chat is already open
      if (isOpen) {
        console.log("❌ Chat is open");
        return false;
      }

      // Check for new/inactive users
      const storedSessionId = getChatSessionId();
      const hasNoSession = !storedSessionId;
      const hasNoMessages = messages.length <= 1; // Only initial message

      console.log("🔍 Onboarding check:", {
        hasNoSession,
        hasNoMessages,
        hasLoadedHistory,
        messagesLength: messages.length,
        storedSessionId: !!storedSessionId
      });

      return (hasNoSession || hasNoMessages) && hasLoadedHistory;
    };

    if (shouldShowOnboarding()) {
      console.log("🎯 Onboarding animation will show in 6 seconds");
      // Show animation after 6 seconds delay
      const showTimer = setTimeout(() => {
        console.log("🎉 Showing onboarding animation");
        setShowOnboarding(true);

        // Auto-dismiss after 15 seconds if no interaction
        // const dismissTimer = setTimeout(() => {
        //   console.log("⏰ Auto-dismissing onboarding animation");
        //   setShowOnboarding(false);
        //   setOnboardingDismissed(true);
        // }, 15000);

        // return () => clearTimeout(dismissTimer);
      }, 3000);

      return () => clearTimeout(showTimer);
    }
  }, [onboardingDismissed, isOpen, messages.length, hasLoadedHistory]);

  // Handle onboarding dismissal
  const handleOnboardingDismiss = useCallback(() => {
    setOnboardingDismissed(true);
    setShowOnboarding(false);
  }, []);

  // Handle onboarding click (open chat)
  const handleOnboardingClick = useCallback(() => {
    setOnboardingDismissed(true);
    setShowOnboarding(false);
    setIsOpen(true);
  }, []);

  // Typewriter effect component
  const TypewriterText = ({ text, speed = 50 }: { text: string; speed?: number }) => {
    const [displayText, setDisplayText] = useState("");
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
      // If user prefers reduced motion, show text immediately
      if (prefersReducedMotion) {
        setDisplayText(text);
        setCurrentIndex(text.length);
        return;
      }

      if (currentIndex < text.length) {
        const timer = setTimeout(() => {
          setDisplayText(prev => prev + text[currentIndex]);
          setCurrentIndex(prev => prev + 1);
        }, speed);

        return () => clearTimeout(timer);
      }
    }, [currentIndex, text, speed, prefersReducedMotion]);

    useEffect(() => {
      // Reset when text changes
      if (prefersReducedMotion) {
        setDisplayText(text);
        setCurrentIndex(text.length);
      } else {
        setDisplayText("");
        setCurrentIndex(0);
      }
    }, [text, prefersReducedMotion]);

    return <span>{displayText}</span>;
  };

  // Onboarding bubble component
  const OnboardingBubble = () => {
    if (!showOnboarding) return null;

    return (
      <motion.div
        className={classes["onboarding-bubble"]}
        initial={{ opacity: 0, x: -20, scale: 0.8 }}
        animate={{
          opacity: 1,
          x: 0,
          scale: [0.8, 1.1, 1],
        }}
        exit={{ opacity: 0, x: -20, scale: 0.8 }}
        transition={{
          duration: 0.6,
          ease: "easeOut",
          scale: {
            duration: 0.6,
            times: [0, 0.6, 1],
            ease: ["easeOut", "easeInOut"]
          }
        }}
        onClick={handleOnboardingClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            handleOnboardingClick();
          }
        }}
        aria-label={t("kiko-onboarding-message")}
      >
        <div className={classes["onboarding-content"]}>
          <TypewriterText text={t("kiko-onboarding-message")} speed={30} />
        </div>
        <button
          className={classes["onboarding-close"]}
          onClick={(e) => {
            e.stopPropagation();
            handleOnboardingDismiss();
          }}
          aria-label="Close"
          type="button"
        >
          <IconX size={16} />
        </button>
        <div className={classes["onboarding-arrow"]} />
      </motion.div>
    );
  };

  // useEffect(() => {
  //   const socket = new WebSocket(import.meta.env.VITE_KIKO_WS_URL as string);

  //   socket.onopen = () => {
  //     console.log("Connected to WebSocket");
  //     // socket.send("Hello Server!");
  //   };

  //   socket.onmessage = (event) => {
  //     let rawData = event.data;
  //     let message;

  //     console.log("Message received")

  //     try {
  //       message = typeof rawData === "string" ? JSON.parse(rawData) : rawData;
  //     } catch (e) {
  //       console.error("Error parsing WebSocket message:", e, rawData);
  //       return;
  //     }

  //     if (message && typeof message === "object" && message?.medium === "website") {
  //       console.log("Message being processed:", message);
  //     }

  //   };

  //   socket.onerror = (error) => {
  //     console.error("WebSocket Error:", error);
  //   };

  //   socket.onclose = () => {
  //     console.log("WebSocket Disconnected");
  //   };

  //   setWs(socket);

  //   return () => {
  //     socket.close();
  //   };
  // }, []);

  return (
    <div className={classes["chat-wrapper"]}>
      <AnimatePresence>
        <OnboardingBubble />
      </AnimatePresence>

      {isOpen && (
        <div className={classes["chat-box"]}>
          <div className={classes["header"]}>Kiko</div>
          <div className={classes["chat-content"]} ref={chatContentRef}>
            {messages.map((msg, index) => {
              const isKiko = msg.sender === "kiko";

              const prev = messages[index - 1];
              const currDate = new Date(msg.timestamp);
              const prevDate = prev ? new Date(prev.timestamp) : null;

              // Check if a date separator should be shown
              let showDateSeparator = false;
              if (
                !prevDate ||
                currDate.toDateString() !== prevDate.toDateString()
              ) {
                showDateSeparator = true;
              }

              const dateLabel = (() => {
                const today = new Date();
                const yesterday = new Date();
                yesterday.setDate(today.getDate() - 1);

                if (currDate.toDateString() === today.toDateString())
                  return "Today";
                if (currDate.toDateString() === yesterday.toDateString())
                  return "Yesterday";

                return currDate.toLocaleDateString(undefined, {
                  month: "short",
                  day: "numeric",
                  year: "numeric",
                });
              })();

              const timeLabel = currDate.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              });

              return (
                <div key={msg.id || index}>
                  {showDateSeparator && (
                    <div className={classes["date-separator"]}>
                      <span>{dateLabel}</span>
                    </div>
                  )}

                  <motion.div
                    className={
                      isKiko
                        ? classes["kiko-chat-item-wrapper"]
                        : classes["human-chat-item-wrapper"]
                    }
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, ease: "easeOut" }}>
                    <div
                      className={
                        isKiko
                          ? classes["kiko-chat-item"]
                          : classes["human-chat-item"]
                      }>
                      <div className={classes["head"]}>
                        <div className={classes["text"]}>
                          {isKiko ? "Kiko" : "You"}
                        </div>
                        {isKiko &&
                          (copiedIndex === index ? (
                            <div
                              style={{ fontSize: "0.75rem", fontWeight: "400" }}
                              className={classes["text"]}>
                              Copied!
                            </div>
                          ) : (
                            <button
                              type="button"
                              onClick={() => {
                                navigator.clipboard.writeText(msg.text);
                                setCopiedIndex(index);
                                setTimeout(() => setCopiedIndex(null), 1500);
                              }}
                              className={classes["copy-icon"]}
                              style={{
                                cursor: "pointer",
                                background: "none",
                                border: "none",
                                padding: 0,
                              }}>
                              <img src={CopyIcon} alt="Copy message" />
                            </button>
                          ))}
                      </div>
                      <div className={classes["body"]}>
                        <MarkdownRenderer markdown={msg.text} />
                      </div>
                    </div>
                    <div className={classes["time-text"]}>{timeLabel}</div>
                  </motion.div>
                </div>
              );
            })}

            {showExamples && (
              <div className={classes["chat-example-box"]}>
                {chatExamples.map((chatExample, index) => (
                  <button
                    key={index}
                    type="button"
                    className={classes["chat-example"]}
                    onClick={() => handleExampleClick(chatExample)}>
                    {chatExample}
                  </button>
                ))}
              </div>
            )}
          </div>

          {loadingHistory && <div className={classes["loading-history"]}></div>}

          {kikoTyping && (
            <div className={classes["dot-loader"]}>
              <span></span>
              <span></span>
              <span></span>
            </div>
          )}

          <div className={classes["disclaimer"]}>
            <span>{t("disclaimer")}</span>
          </div>

          <div className={classes["chat-input-section"]}>
            <input
              className={classes["chat-input"]}
              placeholder={`${t("kiko-ask-anything")}...`}
              type="text"
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              onKeyDown={e => {
                if (e.key === "Enter") handleSend();
              }}
            />
            <IconSend
              className={classes["chat-send"]}
              onClick={handleSend}
              style={{ cursor: "pointer" }}
            />
          </div>
        </div>
      )}

      <button
        type="button"
        className={
          isOpen
            ? classes["chat-button"]
            : showOnboarding
              ? classes["chat-button-with-onboarding"]
              : classes["chat-button-anim"]
        }
        onClick={() => setIsOpen(prev => !prev)}>
        <AnimatePresence mode="wait" initial={false}>
          {isOpen ? (
            <motion.div
              key="close"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}>
              <img
                src={CloseIcon}
                className={classes["chat-icon"]}
                alt="Close"
              />
            </motion.div>
          ) : (
            <motion.div
              key="chatbot"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}>
              <img
                src={ChatBotIcon}
                className={classes["chat-icon"]}
                alt="Chatbot"
              />
            </motion.div>
          )}
        </AnimatePresence>
      </button>
    </div>
  );
};
