@use "~/styles/index" as *;

.loading-history {
  width: 40px;
  height: 40px;
  border: 4px solid #fff;
  border-top-color: var(--mantine-color-accent-4); 
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0px auto;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.dot-loader {
  display: flex;
  align-items: flex-end;
  gap: 6px;
  height: 1.5rem;
}

.dot-loader span {
  width: 8px;
  height: 8px;
  background-color: var(--mantine-color-accent-3);
  border-radius: 50%;
  display: inline-block;
  animation: wave 0.7s infinite ease-in-out;
}

.dot-loader span:nth-child(1) {
  animation-delay: 0s;
}
.dot-loader span:nth-child(2) {
  animation-delay: 0.1s;
}
.dot-loader span:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes wave {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes realisticBounce {

  0%,
  100% {
    transform: translateY(0);
  }

  10% {
    transform: translateY(-40%);
  }

  20% {
    transform: translateY(0);
  }

  30% {
    transform: translateY(-25%);
  }

  40% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-15%);
  }

  60% {
    transform: translateY(0);
  }

  70% {
    transform: translateY(-8%);
  }

  80% {
    transform: translateY(0);
  }

  90% {
    transform: translateY(-4%);
  }
}

.date-separator {
  text-align: center;
  margin: 1.5rem 0;
  font-size: 0.75rem;
  font-weight: 600;
  color: #888;
}

.disclaimer {
  text-align: center;
  font-size: 0.7rem;
  font-weight: 500;
  color: #888;
}


.chat-button {
  display: flex;
  width: 3.25rem;
  height: 3.25rem;
  justify-content: center;
  align-items: center;
  border-radius: 6.1875rem;
  background: var(--mantine-color-accent-5);
}

.chat-button-anim {
  display: flex;
  width: 3.25rem;
  height: 3.25rem;
  justify-content: center;
  align-items: center;
  border-radius: 6.1875rem;
  background: var(--mantine-color-accent-5);

  animation: realisticBounce 0.6s ease-out infinite;
  animation-delay: 0s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-out;
  animation-fill-mode: both;
  animation-play-state: running;
  animation-name: realisticBounce;
  animation-duration: 5s;
}

/* Use animation-delay trick to make bounce only happen at the start of every 10s */
.chat-button::after {
  content: '';
  animation: none;
}

.chat-icon {
  width: 2rem;
  height: 2rem;
}


@keyframes slideFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-wrapper {
  position: fixed;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: end;
  right: 6%;
  left: 6%;
  bottom: 7%;
  z-index: 10;

  @include larger-than($sm) {
    width: 100%;
    max-width: 25.875rem;
    right: 8.5%;
    left: auto;
  }
}

.chat-box {
  display: flex;
  width: 100%;
  height: 34.0625rem;
  padding: 1rem;
  flex-direction: column;
  gap: 1rem;
  border-radius: 0.5rem;
  background: #FFF;
  box-shadow: 0px 4px 24px 0px rgba(74, 24, 255, 0.15);

  opacity: 0;
  transform: translateY(20px);
  animation: slideFadeIn 0.4s ease-out forwards;
}

.chat-content {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  gap: 0.625rem;
  flex: 1;
}

.chat-input-section {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  align-self: stretch;
}

.chat-input {
  display: flex;
  padding: 0.5rem 1rem;
  align-items: center;
  gap: 0.625rem;
  flex: 1;
  border-radius: 6.1875rem;
  background: #F0F1F2;
  outline: none;
  font-size: 16px;

  &:focus {
    outline: 2px solid var(--mantine-color-accent-7);
    outline-offset: 2px;
  }


  @include larger-than($sm) {
    font-size: 1rem;
  }
}


.chat-send {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--mantine-color-accent-5);
}

.header {
  color: #000;
  text-align: center;
  font-size: 1.25rem;
  font-weight: 600;
}

.kiko-chat-item-wrapper {
  display: flex;
  width: 100%;
  padding-right: 1rem;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;

  .kiko-chat-item {
    display: flex;
    padding: 0.9375rem;
    background-color: var(--mantine-color-accent-0);
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 0.375rem;
    align-self: stretch;
    border-radius: 0.9375rem 0.9375rem 0.9375rem 0rem;

    .head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;

      .text {
        color: #3511B5;
        font-size: clamp(0.75rem, 2vw, 1rem);
        font-weight: 600;

        @include larger-than($sm) {
          font-size: 1rem;
        }
      }
    }

    .body {
      color: #2D3036;
      font-size: clamp(0.9rem, 2vw, 1rem);
      font-weight: 400;

      @include larger-than($sm) {
        font-size: 1rem;
      }
    }
  }
}

.human-chat-item-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;

  .human-chat-item {
    display: flex;
    padding: 0.625rem 0.9375rem;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 1rem 1rem 0rem 1rem;
    background-color: #F0F1F2;
    gap: 0.375rem;

    .head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;

      .text {
        color: #6B7280;
        font-size: clamp(0.75rem, 2vw, 1rem);
        font-weight: 600;

        @include larger-than($sm) {
          font-size: 1rem;
        }
      }
    }

    .body {
      color: #2D3036;
      font-size: clamp(0.9rem, 2vw, 1rem);
      font-weight: 400;

      @include larger-than($sm) {
        font-size: 1rem;
      }
    }
  }
}

.time-text {
  color: #6B7280;
  font-size: 0.625rem;
  font-weight: 400;

  @include larger-than($sm) {
    font-size: 0.75rem;
  }
}

.copy-icon {
  width: 1rem;
  height: 1rem;
}

.speaker-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.chat-example-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  gap: 0.625rem;
  align-self: stretch;
}

.chat-example {
  display: flex;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  border-radius: 6.1875rem;
  color: var(--mantine-color-accent-5);
  border: 1px solid var(--mantine-color-accent-5);
  background: var(--White, #FFF);
  font-size: clamp(0.625rem, 2vw, 0.75rem);

  @include larger-than($sm) {
    font-size: 0.75rem;
  }
}

.loading-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  gap: 0.5rem;
}

.loading-text {
  font-size: 0.875rem;
  color: var(--mantine-color-gray-6);
}
