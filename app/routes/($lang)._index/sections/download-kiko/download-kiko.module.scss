@use "~/styles/index" as *;

.section {
  margin-block: 5rem;

  @include larger-than($md) {
    margin-block: 10rem;
  }
  @include larger-than($lg1) {
    margin-block: 13rem;
  }
}

.flex-content {
  display: flex;
  flex-direction: column-reverse;
  gap: 2rem;
  align-items: center;
  background-color: var(--mantine-color-accent-0);
  padding: 2rem 1rem;
  border-radius: 1.5rem;

  @include larger-than($sm) {
    padding-inline: 2rem;
    border-radius: 2rem;
  }
  @include larger-than($md) {
    position: relative;
    gap: 0;
    padding-block: 3rem;
    border-radius: 2.5rem;
    flex-direction: row;
  }
  @include larger-than($lg1) {
    // gap: 1rem;
    padding-block: 5rem;
  }
}

.left-section {
  max-width: rem(500px);
  margin-inline: auto;

  @include larger-than($md) {
    flex-basis: 47%;
  }
  @include larger-than($lg1) {
    flex-basis: 53%;
  }

  img {
    @include larger-than($md) {
      object-fit: contain;
      position: absolute;
      overflow: visible;
      top: -15%;
      left: 0;
      width: 50%;
      //   outline: 1px solid red;
    }
    @include larger-than($lg1) {
      object-fit: cover;
      top: -15%;
      left: 2%;
      width: 48%;
    }
    @include larger-than($lg2) {
      left: 5%;
      width: 45%;
    }
  }
}

.right-section {
  width: 100%;

  @include larger-than($md) {
    flex-basis: 53%;
  }
  @include larger-than($lg1) {
    flex-basis: 47%;
  }

  .heading {
    max-width: 25ch;
  }

  .description {
    margin-top: 1rem;
    max-width: 50ch;
    font-size: rem(15px);

    span {
      font-weight: 600;
      color: var(--mantine-color-accent-6);
    }
  }

  .download-wrapper-container {
    @supports (container-type: inline-size) {
      container-type: inline-size;
    }
  }

  .download-wrapper {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;

    .image {
      width: rem(150px);
    }

    // Forcing Sass interpolation using #{}
    @container (min-width: #{rem(470px)}) {
      flex-direction: row;
    }
  }

  .contact-us {
    margin-top: 2rem;

    a {
      @include flex(row, center, center, 0.25rem);
      width: fit-content;
      // text-decoration: underline;
      color: var(--mantine-color-accent-6);
      font-weight: 500;
    }
  }
}
