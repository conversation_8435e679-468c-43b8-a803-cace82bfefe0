"use client";
import classes from "./download-kiko.module.scss";
import utilityClasses from "~/styles/utilities.module.scss";
import <PERSON><PERSON> from "~/assets/misc/kiko.png";
import AppStoreEN from "~/assets/misc/app-store-en.png";
import AppStoreFR from "~/assets/misc/app-store-fr.png";
import PlaystoreEN from "~/assets/misc/playstore-en.png";
import PlaystoreFR from "~/assets/misc/playstore-fr.png";
import WhatsappEN from "~/assets/misc/whatsapp-en.png";
import WhatsappFR from "~/assets/misc/whatsapp-fr.png";
import MessengerEN from "~/assets/misc/messenger-en.png";
import MessengerFR from "~/assets/misc/messenger-fr.png";
import { Button, Image } from "@mantine/core";
import { Link, useParams } from "@remix-run/react";
import { useTranslation } from "~/hooks";
import { KIKO_MESSENGER_LINK, KIKO_WHATSAPP_LINK, LARUCHE_APPSTORE_LINK, LARUCHE_PLAYSTORE_LINK } from "~/constants";

export const DownloadKiko = () => {
  const { t, i18n } = useTranslation();
  const { lang } = useParams();

  const currentLang = i18n.language.startsWith("fr") ? "fr" : "en";

  const AppStoreImage = currentLang === "fr" ? AppStoreFR : AppStoreEN;
  const PlaystoreImage = currentLang === "fr" ? PlaystoreFR : PlaystoreEN;
  const WhatsappImage = currentLang === "fr" ? WhatsappFR : WhatsappEN;
  const MessengerImage = currentLang === "fr" ? MessengerFR : MessengerEN;


  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <div className={classes["flex-content"]}>
          <div className={classes["left-section"]}>
            <img src={Kiko} alt={t("downloadKikoAlt")} />
          </div>
          <div className={classes["right-section"]}>
            <h2 className={classes.heading}>{t("kikoHeading")}</h2>
            <p className={classes.description}>{t("kikoDescription")}</p>

            <p className={classes.description}>
              {t("availableOn")} <span>{t("availablePlatforms")}</span>
            </p>

            <div className={classes["download-wrapper-container"]}>
              <div className={classes["download-wrapper"]}>
                <a href={LARUCHE_APPSTORE_LINK} target="_blank" rel="noopener noreferrer">
                  <Image
                    className={classes.image}
                    src={AppStoreImage}
                    alt="Download La Ruche Health from the App Store"
                  />
                </a>

                <a href={LARUCHE_PLAYSTORE_LINK} target="_blank" rel="noopener noreferrer">
                  <Image
                    className={classes.image}
                    src={PlaystoreImage}
                    alt="Download La Ruche Health from the Play Store"
                  />
                </a>

                <a href={KIKO_WHATSAPP_LINK}target="_blank" rel="noopener noreferrer">
                  <Image
                    className={classes.image}
                    src={WhatsappImage}
                    alt="Chat with La Ruche Health on WhatsApp"
                  />
                </a>

                <a href={KIKO_MESSENGER_LINK} target="_blank" rel="noopener noreferrer">
                  <Image
                    className={classes.image}
                    src={MessengerImage}
                    alt="Chat with La Ruche Health on Messenger"
                  />
                </a>

              </div>
            </div>

            <div className={classes["contact-us"]}>
              <p>{t("needWhatsappChatbot")}</p>

              <Button
                component={Link}
                to={`/${lang || currentLang}/chatbot`}
                size="md"
                c="white"
                mt={"1rem"}>
                {t("learnMore")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
