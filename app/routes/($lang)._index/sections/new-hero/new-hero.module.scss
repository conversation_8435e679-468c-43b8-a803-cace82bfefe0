@use "~/styles/index" as *;

.inner-search-button {
  display: none;
  padding: 8px 32px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 99px;
  border: 1px solid #EFEFEF;
  background: #FFF;

  &:hover {
    border: 1px solid #AAA;
    cursor: pointer;
  }

  @include larger-than($sm) {
    display: block;
  }
}

.search-input-div {
  display: flex;
  gap: 0.6rem;
  width: 100%;
  max-width: 740px;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  background-color: white;
  align-items: center;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  padding-left: 1rem;
  padding-right: 1rem;
  border: 1px solid #EFEFEF;
  margin-inline: auto;

  @include larger-than($sm) {
    font-size: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-right: 0.5rem;
  }
}

.search-wrapper {
  width: 100%;
  position: relative;
}

.search-input-container {
  flex: 1;
}

.search-input {
  color: #686868;
  flex: 1;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 1rem;

  @include larger-than($sm) {
    font-size: 1rem;
  }
}

.search-results-div {
  width: 100%;
  max-width: 740px;
  margin-inline: auto;

  @include larger-than($sm) {
    font-size: 1rem;
  }
}

.search-results-group {
  padding: 0.5rem;
}

.search-results-heading {
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;

  &:hover {
    background-color: var(--mantine-color-accent-4);
    color: white;

    .search-result-specialty {
      color: #DDD;
    }
  }
}

.search-result-image {
  width: 30px;
  height: 30px;
  border-radius: 100vmax;
  margin-right: 0.5rem;
}

.search-result-name {
  font-weight: 500;
}

.search-result-specialty {
  font-size: 0.85rem;
  color: #555;
}

.section {
  position: relative;
  min-height: 39.4375rem;
  padding-bottom: 4rem;
  @include flex($direction: row, $justify: center, $align: center, $gap: 1rem);
  background-image: url('../../../../assets/hero/mobile-hero-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  @include size("100%", "100vh");

  @include larger-than($sm) {
    background-image: url('../../../../assets/hero/web-hero-bg.png');
  }

  &::before {
    @include pseudo;
    inset: 0;
    z-index: -1;
  }
}

.flex-content {
  @include size("inherit");
  @include flex($direction: column, $gap: 1rem);
  padding-top: 3rem;

  @include larger-than($md) {
    padding-block: 2rem;
    @include flex($direction: row, $justify: center, $align: center, $gap: 1rem);
  }

  @include larger-than($lg1) {
    padding-block: 0;
    gap: 2.5rem;
  }
}

.mid-section {
  width: 100%;

  .heading {
    font-weight: 600;
    line-height: normal;
    text-align: center;
    max-width: 20ch;
    font-size: clamp(rem(40px), 3.5vw, rem(61px));
    margin-inline: auto;

    @include larger-than($md) {
      font-size: clamp(rem(50px), 3.5vw, rem(61px));
    }

    span {
      color: var(--mantine-color-accent-5);
    }
  }

  .description {
    margin-top: 1rem;
    text-align: center;
    max-width: 55ch;
    margin-inline: auto;
    font-size: clamp(0.9rem, 2.5vw, 1.25rem);
    font-style: normal;
    font-weight: 500;
  }

  .search-div {
    display: flex;
    width: 100%;
    margin-top: 3rem;
    align-items: center;
    gap: 0.625rem;
  }

  .search-button {
    padding: 0.5rem 1rem;

    @include larger-than($md) {
      padding: 0.5rem 2rem;
    }
  }

  .talk-button {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    border-radius: 999px;
    font-size: 0.85rem;
    background-color: var(--mantine-color-accent-5);
    transition: background-color 0.2s ease;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    @include larger-than($sm) {
      font-size: 1rem;
      padding-left: 2rem;
      padding-right: 2rem;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
    }
  }

  .book-test-button {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    border-radius: 999px;
    font-size: 0.85rem;
    border: solid;
    box-sizing: border-box;
    border-color: var(--mantine-color-accent-5);
    transition: background-color 0.2s ease;
    color: var(--mantine-color-accent-5);
    display: flex;
    align-items: center;
    gap: 0.5rem;

    @include larger-than($sm) {
      font-size: 1rem;
      padding-left: 2rem;
      padding-right: 2rem;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
    }
  }

  .talk-button:hover {
    background-color: var(--mantine-color-accent-6);
  }

  .talk-div {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 1.5rem;
    gap: 1.2rem;
    font-weight: 500;
    font-size: 1rem;

    @include larger-than($sm) {
      font-size: 0.75rem;
      gap: 0.75rem;
      flex-direction: row;
    }
  }
}