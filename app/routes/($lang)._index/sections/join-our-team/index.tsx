"use client";

import { Button, Group, Image, Stack, Text } from "@mantine/core";
import utilityClasses from "~/styles/utilities.module.scss";
import Mockup1 from "~/assets/misc/mockup-1.png";
import classes from "./join-our-team.module.scss";
import { useTranslation } from "~/hooks";
import { JOIN_OUR_TEAM_URL } from "~/constants";

export const JoinOurTeam = () => {
  const { t } = useTranslation();

  return (
    <section className={classes.section}>
      <div className={utilityClasses.container}>
        <div className={classes["flex-content"]}>
          <div className={classes["text-section"]}>
            <Text
              maw={"55ch"}
              fw={500}
              fz={{ base: 25, sm: 30, md: 35 }}
              lh={1.3}>
              {t("specialistHeading")}
              <br />
              {t("joinHeading")}
            </Text>
            <Text maw={"55ch"}>{t("specialistJoinDescription")}</Text>
            <Button component="a" href={JOIN_OUR_TEAM_URL} bg="white" c="accent" w="fit-content" size="md" fw={500}>
              {t("joinOurTeamButton")}
            </Button>
          </div>
          <div className={classes["img-wrapper"]}>
            <img src={Mockup1} alt={t("joinOurTeamImageAlt")} />
          </div>
        </div>
      </div>
    </section>
  );
};
