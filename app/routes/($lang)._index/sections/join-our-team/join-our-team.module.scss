@use "~/styles/index" as *;

.section {
  margin-top: 5rem;

  @include larger-than($md) {
    margin-top: 7rem;
  }
}

.flex-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--mantine-spacing-lg);
  gap: 2rem;
  width: 100%;

  @include larger-than($md) {
    align-items: stretch; // This ensures equal heights
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
}

.text-section {
  padding-block: 3rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  color: white;
  background-color: var(--mantine-color-accent-7);
  width: 100%;
  padding-inline: 1rem;
  border-radius: 1rem;

  @include larger-than($sm) {
    padding-inline: 2rem;
  }

  p {
    @include larger-than($lg1) {
      max-width: 18ch;
    }
  }
}

.img-wrapper {
  height: 100%;

  img {
    @include size(100%);
    border-radius: 1rem;
  }
}
