"use client";

import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./get-the-app.module.scss";
import Hero1<PERSON> from "~/assets/hero/1-en.png";
import Hero2EN from "~/assets/hero/2-en.png";
import Hero3<PERSON> from "~/assets/hero/3-en.png";
import Hero4<PERSON> from "~/assets/hero/4-en.png";
import Hero1FR from "~/assets/hero/1-fr.png";
import Hero2FR from "~/assets/hero/2-fr.png";
import Hero3FR from "~/assets/hero/3-fr.png";
import Hero4FR from "~/assets/hero/4-fr.png";
import Calendar from "~/assets/get-app/Calendar.svg";
import Expert from "~/assets/get-app/Expert.png";
import Heart from "~/assets/get-app/Heart.png";
import Subtract from "~/assets/get-app/Subtract.png";
import AppStoreEN from "~/assets/misc/app-store-en.png";
import AppStoreFR from "~/assets/misc/app-store-fr.png";
import PlaystoreEN from "~/assets/misc/playstore-en.png";
import PlaystoreFR from "~/assets/misc/playstore-fr.png";
import WhatsappEN from "~/assets/misc/whatsapp-en.png";
import WhatsappFR from "~/assets/misc/whatsapp-fr.png";
import MessengerEN from "~/assets/misc/messenger-en.png";
import MessengerFR from "~/assets/misc/messenger-fr.png";
import { Image } from "@mantine/core";
import { useEffect, useState, useMemo } from "react";
import { useTranslation } from "~/hooks";
import { KIKO_MESSENGER_LINK, KIKO_WHATSAPP_LINK, LARUCHE_APPSTORE_LINK, LARUCHE_PLAYSTORE_LINK } from "~/constants";

export const GetTheApp = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { t, i18n } = useTranslation();

  const images = useMemo(() => {
    if (i18n.language.startsWith("fr")) {
      return [Hero1FR, Hero2FR, Hero3FR, Hero4FR];
    } else {
      return [Hero1EN, Hero2EN, Hero3EN, Hero4EN];
    }
  }, [i18n.language]);

  const lang = i18n.language.startsWith("fr") ? "fr" : "en";

  const AppStoreImage = lang === "fr" ? AppStoreFR : AppStoreEN;
  const PlaystoreImage = lang === "fr" ? PlaystoreFR : PlaystoreEN;
  const WhatsappImage = lang === "fr" ? WhatsappFR : WhatsappEN;
  const MessengerImage = lang === "fr" ? MessengerFR : MessengerEN;


  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [images.length]);

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <h2 className={classes.heading}>{t("getTheAppHeading")}</h2>
        <p className={classes["sub-heading"]}>{t("getTheAppSubHeading")}</p>

        <div className={classes["cards-container"]}>
          <div className={classes["left-section"]}>
            <div className={classes["image-stack"]}>
              {images.map((image, index) => (
                <div
                  key={index}
                  className={classes["image-wrapper"]}
                  style={{
                    opacity: currentIndex === index ? 1 : 0,
                  }}>
                  <img src={image} alt="" />
                </div>
              ))}
            </div>
          </div>

          <div className={classes["icon-text-section"]}>
            <div className={classes["icon-text-section"]}>
              <div className={classes["connector-line"]} />

              <div className={classes["icon-text-div"]}>
                <div className={classes["icon-div"]}>
                  <img className={classes["icon"]} src={Calendar} alt="" />
                </div>
                <div className={classes["text-div"]}>
                  <div className={classes["text-heading"]}>
                    {t("bookCareHeading")}
                  </div>
                  <div className={classes["text-body"]}>
                    {t("bookCareBody")}
                  </div>
                </div>
              </div>

              <div className={classes["icon-text-div"]}>
                <div className={classes["icon-div"]}>
                  <img className={classes["icon"]} src={Expert} alt="" />
                </div>
                <div className={classes["text-div"]}>
                  <div className={classes["text-heading"]}>
                    {t("healthExpertHeading")}
                  </div>
                  <div className={classes["text-body"]}>
                    {t("healthExpertBody")}
                  </div>
                </div>
              </div>

              <div className={classes["icon-text-div"]}>
                <div className={classes["icon-div"]}>
                  <img className={classes["icon"]} src={Subtract} alt="" />
                </div>
                <div className={classes["text-div"]}>
                  <div className={classes["text-heading"]}>
                    {t("followUpCareHeading")}
                  </div>
                  <div className={classes["text-body"]}>
                    {t("followUpCareBody")}
                  </div>
                </div>
              </div>

              <div className={classes["icon-text-div"]}>
                <div className={classes["icon-div"]}>
                  <img className={classes["icon"]} src={Heart} alt="" />
                </div>
                <div className={classes["text-div"]}>
                  <div className={classes["text-heading"]}>
                    {t("stayOnTopHeading")}
                  </div>
                  <div className={classes["text-body"]}>
                    {t("stayOnTopBody")}
                  </div>
                </div>
              </div>
            </div>

            <div className={classes["download-wrapper"]}>
              <a href={LARUCHE_APPSTORE_LINK} target="_blank" rel="noopener noreferrer">
                <Image
                  className={classes.image}
                  src={AppStoreImage}
                  alt="Download La Ruche Health from the App Store"
                />
              </a>

              <a href={LARUCHE_PLAYSTORE_LINK} target="_blank" rel="noopener noreferrer">
                <Image
                  className={classes.image}
                  src={PlaystoreImage}
                  alt="Download La Ruche Health from the Play Store"
                />
              </a>

              <a href={KIKO_WHATSAPP_LINK} target="_blank" rel="noopener noreferrer">
                <Image
                  className={classes.image}
                  src={WhatsappImage}
                  alt="Chat with La Ruche Health on WhatsApp"
                />
              </a>

              <a href={KIKO_MESSENGER_LINK} target="_blank" rel="noopener noreferrer">
                <Image
                  className={classes.image}
                  src={MessengerImage}
                  alt="Chat with La Ruche Health on Messenger"
                />
              </a>

            </div>
          </div>

          <div className={classes["left-section-mobile"]}>
            <div className={classes["image-stack"]}>
              {images.map((image, index) => (
                <div
                  key={index}
                  className={classes["image-wrapper"]}
                  style={{
                    opacity: currentIndex === index ? 1 : 0,
                  }}>
                  <img src={image} alt="" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
