@use "~/styles/index" as *;

.section {
  margin-top: 5rem;

  @include larger-than($md) {
    margin-top: 7rem;
  }
}

.left-section {
  z-index: 1;
  display: none;

  @include larger-than($md) {
    display: block;
    flex-basis: 50%;
    min-width: rem(450px);
  }

  .image-stack {
    position: relative;
    width: min(100%, rem(550px));
    height: 100%;
    margin-inline: auto;
    aspect-ratio: 1;
    z-index: 9;
  }

  .image-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: min(100%, rem(550px));
    transition: opacity 1s ease;
    // outline: 1px solid red;
  }
}

.left-section-mobile {
  z-index: 1;
  display: block;
  
  @include larger-than($md) {
    display: none;
    flex-basis: 50%;
    min-width: rem(450px);
  }

}

.image-stack {
  position: relative;
  width: min(100%, rem(550px));
  height: 100%;
  margin-inline: auto;
  aspect-ratio: 1;
  z-index: 9;
}

.image-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: min(100%, rem(550px));
  transition: opacity 1s ease;
  // outline: 1px solid red;
}

.connector-line {
  position: absolute;
  width: 0.25rem;
  height: 90%;
  left: 2rem;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  background-color: var(--mantine-color-accent-0);

  @include larger-than($md) {
    left: 3rem;
  }
}

.download-wrapper {
  @include flex(row, center, center, 1rem);
  flex-wrap: wrap;
  margin-top: 2rem;

  @include larger-than($md) {
    justify-content: center;
    padding-left: 3.5rem;
  }

  .image {
    width: rem(150px);
  }
}

.heading {
  text-align: center;
}

.sub-heading {
  text-align: center;
  margin-top: 0.625rem;
  max-width: 60ch;
  font-weight: 500;
  color: var(--mantine-color-accent-8);
  margin-inline: auto;
}

.icon {
  width: 2rem;
  height: 2rem;

  @include larger-than($md) {
    width: 3rem;
    height: 3rem;
  }
}

.icon-text-section {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 1.75rem;
  margin-inline: auto;
  
  @include larger-than($sm) {
    gap: 2.5rem;
  }
}

.icon-text-div {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  align-items: center;
  
  @include larger-than($sm) {
    gap: 1.5rem;
  }
}

.text-heading {
  color: var(--mantine-color-accent-8);
  font-size: 1rem;
  font-weight: 600;
  
  @include larger-than($sm) {
    font-size: 1.25rem;
  }
}

.text-body {
  color: var(--mantine-color-accent-8);
  font-size: 0.75rem;
  max-width: 34ch;
  font-weight: 400;
  
  @include larger-than($sm) {
    font-size: 1rem;
  }
}

.text-div {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.icon-div {
  min-width: 4rem;
  min-height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--mantine-color-accent-0);
  border-radius: 99px;

  @include larger-than($md) {
    width: 6rem;
    height: 6rem;
  }
}

.cards-container {
  display: grid;
  grid-template-columns: 1fr;
  align-items: start;
  gap: 2rem;
  margin-top: 3.5rem;


  @include larger-than($md) {
    grid-template-columns: 0.5fr 0.5fr;
  }

  .card {
    border-radius: 0.5rem;
    box-shadow: 0 0 1rem rgba(74, 24, 255, 0.1);
    padding: 1.5rem;

    &-heading {
      font-size: clamp(rem(18px), 5vw, rem(20px));
      font-weight: 600;
      margin-top: 1.5rem;
    }

    &-description {
      font-size: rem(15px);
      margin-top: 0.75rem;
    }

    .icon-wrapper {
      @include size(rem(50px));
      @include flex-center;
      border-radius: 0.25rem;
      background-color: var(--mantine-color-accent-0);
    }
  }
}