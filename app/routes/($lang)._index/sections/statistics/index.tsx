"use client";

import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./statistics.module.scss";
import { Center, Stack, Text, Title } from "@mantine/core";
import { useTranslation } from "~/hooks";

export const Statistics = () => {
  const { t } = useTranslation();

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <Title order={2} className={classes.heading}>
          {t("ivoryCoastHealthInsights")}
        </Title>
        <Title order={2} className={classes.heading}>
          {t("whatTheDataSays")}
        </Title>

        <div className={classes.grid}>
          <Stack gap={"xs"} className={classes["grid-item"]}>
            <Text fw={600} ta={"center"} className={classes.statistic} lh={1.2}>
              300k+
            </Text>
            <Text ta={"center"} maw={"25ch"} mx="auto">
              {t("healthQuestionsAnswered")}
            </Text>
          </Stack>

          <Stack gap={"xs"} className={classes["grid-item"]}>
            <Text fw={600} ta={"center"} className={classes.statistic} lh={1.2}>
              65%
            </Text>
            <Text ta={"center"} maw={"25ch"} mx="auto">
              {t("youthDiscussingHealth")}
            </Text>
          </Stack>

          <Stack gap={"xs"} className={classes["grid-item"]}>
            <Text fw={600} ta={"center"} className={classes.statistic} lh={1.2}>
              41%
            </Text>
            <Text ta={"center"} maw={"25ch"} mx="auto">
              {t("femalesReproductiveHealth")}
            </Text>
          </Stack>

          <Stack
            hiddenFrom="md"
            mx={"auto"}
            gap={"xs"}
            className={classes["grid-item"]}>
            <Text fw={600} ta={"center"} className={classes.statistic} lh={1.2}>
              {t("popularTopicsTitle")}
            </Text>
            <Text ta={"center"} maw={"30ch"} mx="auto">
              {t("popularTopicsList")}
            </Text>
          </Stack>
        </div>

        <Center className={classes["show-md"]} mt={"3rem"}>
          <Stack mx={"auto"} gap={"xs"} className={classes["grid-item"]}>
            <Text fw={600} ta={"center"} className={classes.statistic} lh={1.2}>
              {t("popularTopicsTitle")}
            </Text>
            <Text ta={"center"} maw={"30ch"} mx="auto">
              {t("popularTopicsList")}
            </Text>
          </Stack>
        </Center>
      </div>
    </div>
  );
};
