@use "~/styles/index" as *;

.section {
  margin-top: 5rem;

  @include larger-than($md) {
    margin-top: 7rem;
  }
}
.show-md {
  display: none;

  @include larger-than($md) {
    display: block;
  }
}

.heading {
  text-align: center;
  font-weight: 600;
  font-size: clamp(
    rem(32px),
    5vw,
    rem(50px)
  ); // todo: If this is repeated in other places, create a variable
}

.grid {
  display: grid;
  grid-template-columns: 1fr;
  margin-top: 2rem;
  gap: 2rem;

  @include larger-than($sm) {
    grid-template-columns: 1fr 1fr;
  }
  @include larger-than($md) {
    grid-template-columns: 1fr 1fr 1fr;
    justify-content: space-between;
  }

  &-item {
    .statistic {
      font-size: clamp(rem(32px), 5vw, rem(50px));
      color: var(--mantine-color-accent-5);
    }
  }
}
