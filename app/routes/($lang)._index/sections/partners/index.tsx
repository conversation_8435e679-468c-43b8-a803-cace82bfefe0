"use client";

import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./partners.module.scss";
import { Carousel } from "@mantine/carousel";
import { Image } from "@mantine/core";
import Autoplay from "embla-carousel-autoplay";
import { useRef } from "react";
import { useTranslation } from "~/hooks";

// Updated imports
import BBCImage from "~/assets/partners/bbc.png";
import BMGImage from "~/assets/partners/bmg.png";
import NCIImage from "~/assets/partners/nci.png";
import GoogleImage from "~/assets/partners/google.png";
import HoaqImage from "~/assets/partners/hoaq.png";
import PulseImage from "~/assets/partners/pulse.png";
import RAEImage from "~/assets/partners/rae.png";
import RFIImage from "~/assets/partners/rfi.png";
import SFAImage from "~/assets/partners/sfa.png";
import UmedImage from "~/assets/partners/umed.png";
import UOCImage from "~/assets/partners/uoc.jpg";
import KonosImage from "~/assets/partners/konos.jpg";

const partnersImages = [
  { name: "BBC", image: BBCImage },
  { name: "BMG", image: BMGImage },
  { name: "Google", image: GoogleImage },
  { name: "Hoaq", image: HoaqImage },
  { name: "Pulse", image: PulseImage },
  { name: "RAE", image: RAEImage },
  { name: "RFI", image: RFIImage },
  { name: "SFA", image: SFAImage },
  { name: "Umed", image: UmedImage },
  { name: "UOC", image: UOCImage },
  { name: "NCI", image: NCIImage },
  { name: "Konos", image: KonosImage },
];

export const Partners = () => {
  const { t } = useTranslation();
  const autoplay = useRef(Autoplay({ delay: 2000 }));

  return (
    <div className={utilityClasses.container}>
      <div className={classes.section}>
        <h2 className={classes.heading}>{t("trustedPartners")}</h2>

        <Carousel
          mt={{ base: "1rem", md: 0 }}
          w="100%"
          plugins={[autoplay.current]}
          onMouseEnter={autoplay.current.stop}
          onMouseLeave={autoplay.current.reset}
          loop
          speed={3}
          withControls={false}
          slideSize={{ base: "33.333333%", md: "25%", lg2: "20%" }}
          slideGap={{ base: "lg", sm: "xl", lg1: "2rem" }}
          align="center"
          slidesToScroll={1}>
          {partnersImages.map((partner, index) => (
            <Carousel.Slide key={index} className={classes.slide}>
              <Image
                fit="contain"
                w={150}
                h={100}
                src={partner.image}
                alt={partner.name}
              />
            </Carousel.Slide>
          ))}
        </Carousel>
      </div>
    </div>
  );
};
