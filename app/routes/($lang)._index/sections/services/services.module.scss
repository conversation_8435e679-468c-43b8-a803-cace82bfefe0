@use "~/styles/index" as *;

.section {
  margin-top: 5rem;

  @include larger-than($md) {
    margin-top: 7rem;
  }
}

.heading {
  text-align: center;
}

.sub-heading {
  text-align: center;
  margin-top: 0.5rem;
  max-width: 40ch;
  margin-inline: auto;
}

.cards-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-top: 3rem;
  max-width: rem(800px);
  margin-inline: auto;

  @include larger-than($sm) {
    grid-template-columns: 1fr 1fr;
  }
  @include larger-than($md) {
    // grid-template-columns: 1fr 1fr 1fr;
  }

  .card {
    border-radius: 0.5rem;
    box-shadow: 0 0 1rem rgba(74, 24, 255, 0.1);
    padding: 1.5rem;

    &-heading {
      font-size: clamp(rem(18px), 5vw, rem(20px));
      font-weight: 600;
      margin-top: 1.5rem;
    }

    &-description {
      font-size: rem(15px);
      margin-top: 0.75rem;
    }

    .icon-wrapper {
      @include size(rem(50px));
      @include flex-center;
      border-radius: 0.25rem;
      background-color: var(--mantine-color-accent-0);
    }
  }
}
