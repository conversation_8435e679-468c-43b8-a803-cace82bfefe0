import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./services.module.scss";
import {
  IconDeviceMobile,
  IconHome,
  IconMicroscope,
  IconRobot,
} from "@tabler/icons-react";

export const Services = () => {
  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <h2 className={classes.heading}>Our Services</h2>
        <p className={classes["sub-heading"]}>
          Your Health, Simplified: Convenient Care When and Where You Need It
        </p>

        <div className={classes["cards-container"]}>
          <div className={classes.card}>
            <div className={classes["icon-wrapper"]}>
              <IconDeviceMobile
                size={30}
                stroke={1.5}
                color="var(--mantine-color-accent-6)"
              />
            </div>

            <p className={classes["card-heading"]}>Telemedicine</p>
            <p className={classes["card-description"]}>
              Quickly book and speak with a licensed doctor or
              specialist through secure video or phone calls,
              receiving expert care wherever you are.
            </p>
          </div>
          <div className={classes.card}>
            <div className={classes["icon-wrapper"]}>
              <IconHome
                size={30}
                stroke={1.5}
                color="var(--mantine-color-accent-6)"
              />
            </div>

            <p className={classes["card-heading"]}>At-Home Care</p>
            <p className={classes["card-description"]}>
              Experience personalized healthcare with our at-home services,
              including diagnostics, nursing care, and more.
            </p>
          </div>
          <div className={classes.card}>
            <div className={classes["icon-wrapper"]}>
              <IconMicroscope
                size={30}
                stroke={1.5}
                color="var(--mantine-color-accent-6)"
              />
            </div>

            <p className={classes["card-heading"]}>At-Home Labs & Imaging</p>
            <p className={classes["card-description"]}>
              Easily schedule lab tests and imaging services at home,
              with results securely delivered to your phone.
            </p>
          </div>
          <div className={classes.card}>
            <div className={classes["icon-wrapper"]}>
              <IconRobot
                size={30}
                stroke={1.5}
                color="var(--mantine-color-accent-6)"
              />
            </div>

            <p className={classes["card-heading"]}>24/7 Health Assistance</p>
            <p className={classes["card-description"]}>
              Get fast medical advice, schedule services, or learn about our offerings.
              Talk to KIKO, your smart virtual assistant, 24/7 via WhatsApp or our iOS/Android app.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
