"use client";

import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./teleconsultation-specialties.module.scss";
import { Button } from "@mantine/core";
import { Carousel } from "@mantine/carousel";
import Autoplay from "embla-carousel-autoplay";
import { useRef } from "react";
import teleconsultationImage from "~/assets/specialties/online.png";
import { teleconsultationSpecialtiesData } from "~/data";
import { TranslationKey, useTranslation } from "~/hooks";
import { APPOINTMENT_BOOKING_WEBSITE_URL } from "~/constants";

export const TeleconsultationSpecialties = () => {
  const autoplay = useRef(Autoplay({ delay: 2000 }));
  const { t } = useTranslation();

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <h2 className={classes.heading}>{t("teleconsultationHeading")}</h2>
        <p className={classes["sub-heading"]}>
          {t("teleconsultationSubHeading")}
        </p>

        <div className={classes["flex-content"]}>
          <div className={classes["left-section"]}>
            <div className={classes["img-wrapper"]}>
              <p className={classes.description}>
                {t("teleconsultationDescription")}
              </p>
              <img src={teleconsultationImage} alt="" />
            </div>
          </div>

          <div className={classes["right-section"]}>
            <Carousel
              flex={"1 0"}
              mt={{ base: "1rem", md: 0 }}
              w={{
                base: "100%",
                md: "calc(50vw - 1rem)",
                lg1: "calc(55vw - 1rem)",
              }}
              plugins={[autoplay.current]}
              onMouseEnter={autoplay.current.stop}
              onMouseLeave={autoplay.current.reset}
              loop
              speed={3}
              controlSize={40}
              slideSize={{
                base: "100%",
                sm: "50%",
                md: "50%",
                lg1: "40%",
                lg2: "40%",
              }}
              slideGap={{ base: "lg" }}
              align="center"
              slidesToScroll={1}>
              {teleconsultationSpecialtiesData.map((specialty, index) => (
                <Carousel.Slide key={index} className={classes.slide}>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      height: "100%",
                      flex: 1,
                    }}>
                    <div className={classes["card-img-wrapper"]}>
                      <img
                        className={classes["card-image"]}
                        src={specialty.image}
                        alt={t(
                          `teleconsultationSpecialties.${specialty.key}.title` as TranslationKey
                        )}
                      />
                    </div>
                    <div
                      style={{
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                      }}
                      className={classes["text-section"]}>
                      <p className={classes["card-title"]}>
                        {t(
                          `teleconsultationSpecialties.${specialty.key}.title` as TranslationKey
                        )}
                      </p>
                      <p className={classes["card-description"]}>
                        {t(
                          `teleconsultationSpecialties.${specialty.key}.description` as TranslationKey
                        )}
                      </p>
                      <div style={{ display: "flex", flex: 1 }} />
                      <Button component="a" href={APPOINTMENT_BOOKING_WEBSITE_URL} mt={"1.5rem"} w={"100%"}>
                        {t("bookAppointmentButton")}
                      </Button>
                    </div>
                  </div>
                </Carousel.Slide>
              ))}
            </Carousel>
          </div>
        </div>
      </div>
    </div>
  );
};
