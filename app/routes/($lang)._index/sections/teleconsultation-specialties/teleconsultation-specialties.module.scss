@use "~/styles/index" as *;

.section {
  margin-top: 5rem;

  @include larger-than($md) {
    margin-top: 10rem;
  }
}

.carousel-div {
  display: flex;
  gap: 1rem;
  background-color: green;
  // padding: 0px 32px;
  justify-content: space-between;
  align-items: center;
}

.img-bg {
  display: flex;
  width: 100%;
  max-width: 584px;
  height: 488px;
  gap: 2rem;
  flex-shrink: 0;
  background-image: url(../../../../assets/specialties/home-med.png);
  border-radius: 1rem;
}

.img-div {
  border-radius: 1rem;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 40px;
  align-items: flex-end;
  gap: 10px;
  flex-shrink: 0;
  background: linear-gradient(180deg, rgba(11, 2, 73, 0.00) 54.47%, #2B2540 91.7%);

  color: #FFF;

  font-size: 20px;
  font-weight: 500;
}

.sub-heading {
  text-align: center;
  margin-top: 0.5rem;
  max-width: 60ch;
  font-weight: 500;
  margin-inline: auto;
  color: var(--mantine-color-accent-8)
}

.heading {
  margin-top: 3rem;
  text-align: center;
  max-width: 30ch;
  margin-inline: auto;

  color: var(--mantine-color-accent-8) span {
    color: var(--mantine-color-accent-5);
  }
}

.flex-content {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  margin-top: 2rem;

  @include larger-than($md) {
    margin-top: 3.5rem;
    flex-direction: row;
    gap: 1.25rem; // This should be roughly same as space between slides in carousel
  }

  @include larger-than($lg1) {
    gap: 1.8rem;
  }
}

.left-section {
  // background-color: var(--mantine-color-accent-0);
  // padding: 2rem 1rem;
  border-radius: 1rem;

  @include larger-than($md) {
    flex-basis: 50%;
    // padding: 2.5rem 1.5rem;
  }

  @include larger-than($lg1) {
    flex-basis: 40%;
  }

  @include larger-than($lg2) {
    flex-basis: 35%;
    min-width: rem(400px);
  }

  .img-wrapper {
    position: relative;
    @include size(100%);
    border-radius: inherit;
    overflow: hidden;

    &::before {
      @include pseudo;
      inset: 0;
      background: linear-gradient(to top,
          rgb(11, 2, 73, 0.7) 10%,
          rgb(0, 0, 0, 0));
      border-radius: inherit;
    }

    img {
      border-radius: inherit;
    }

    .description {
      position: absolute;
      left: rem(15px);
      bottom: rem(25px);
      right: rem(15px);
      color: var(--mantine-color-white);
      max-width: 40ch;

      @include larger-than($sm) {
        left: rem(25px);
        right: rem(25px);
        max-width: 60ch;
      }
    }
  }

  .heading {
    @include larger-than($sm) {
      width: 85%;
    }

    @include larger-than($lg2) {
      width: unset;
    }

    span {
      color: var(--mantine-color-accent-5);
    }
  }

  .description {
    margin-top: 1rem;
    font-size: rem(15px);
  }
}

.right-section {
  @include larger-than($sm) {
    position: relative;
    @include size(100%);

    &::before,
    &::after {
      @include pseudo;
      top: 0;
      @include size(rem(50px), 100%);
      z-index: 1;
      pointer-events: none;
      // outline: 1px solid red;
    }

    &::before {
      left: 0;
      background: linear-gradient(to right,
          rgb(255, 255, 255, 1),
          60%,
          rgb(0, 0, 0, 0));
    }

    &::after {
      right: 0;
      background: linear-gradient(to left,
          rgb(255, 255, 255, 1),
          60%,
          rgb(0, 0, 0, 0));
    }

    flex-grow: 1;
  }

  // @include larger-than($md) {
  //   flex-basis: 50%;
  //   flex-grow: 1;
  // }

  .slide {
    .card-img-wrapper {
      aspect-ratio: 5/3;
      border-radius: 1rem 1rem 0 0;
      overflow: hidden;
    }

    .text-section {
      background-color: var(--mantine-color-accent-0);
      padding: 1.5rem 1rem 2rem;
      border-radius: 0 0 1rem 1rem;
      height: auto;

      .card-title {
        font-size: clamp(rem(20px), 4vw, rem(22px));
        font-weight: 600;

        @include larger-than($md) {
          font-size: clamp(rem(18px), 4vw, rem(20px));
        }
      }

      .card-description {
        margin-top: 0.5rem;
        font-size: rem(15px);
      }
    }
  }
}