@use "~/styles/index" as *;

.section {
  margin-top: 5rem;

  @include larger-than($md) {
    margin-top: 10rem;
  }
}

.heading {
  text-align: center;
  max-width: 34ch;
  margin-inline: auto;

  span {
    color: var(--mantine-color-accent-5);
  }
}

.cards-container {
  display: grid;
  gap: 2rem;
  margin-top: 2.5rem;

  @include larger-than($sm) {
    grid-template-columns: 1fr 1fr;
    gap: 2rem 1.5rem;
  }
  @include larger-than($md) {
    grid-template-columns: repeat(4, 1fr);
    // gap: 2rem;
    margin-top: 3rem;
  }

  .card {
    .img-wrapper {
      border-radius: 1rem;
      overflow: hidden;
    }

    &-title {
      margin-top: 1rem;
      font-size: rem(22px);
      font-weight: 600;
    }

    &-description {
      margin-top: 0.5rem;
    }
  }
}
