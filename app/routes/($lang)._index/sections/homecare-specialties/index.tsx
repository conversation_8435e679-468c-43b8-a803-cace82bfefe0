"use client";

import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./homecare-specialties.module.scss";
import { homecareSpecialtiesData } from "~/data";
import { TranslationKey, useTranslation } from "~/hooks";

export const HomecareSpecialties = () => {
  const { t } = useTranslation();

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <h2 className={classes.heading}>
          {t("homecareHeadingPart1")} <span>{t("homecareHeadingPart2")}</span>{" "}
          {t("homecareHeadingPart3")}
        </h2>

        <div className={classes["cards-container"]}>
          {homecareSpecialtiesData.map((item, index) => (
            <div className={classes.card} key={index}>
              <div className={classes["img-wrapper"]}>
                <img
                  src={item.image}
                  className={classes["card-image"]}
                  alt={t(
                    `homecareSpecialties.${item.key}.title` as TranslationKey
                  )}
                />
              </div>
              <p className={classes["card-title"]}>
                {t(`homecareSpecialties.${item.key}.title` as TranslationKey)}
              </p>
              <p className={classes["card-description"]}>
                {t(
                  `homecareSpecialties.${item.key}.description` as TranslationKey
                )}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
