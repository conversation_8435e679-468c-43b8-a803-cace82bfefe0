@use "~/styles/index" as *;

.section {
  position: relative;
  @include size("100%", "100vh");

  &::before {
    @include pseudo;
    inset: 0;
    background-color: var(--mantine-color-accent-0);
    z-index: -1;
    border-radius: 0 0 100vmax 0;
  }
}

.flex-content {
  @include size("inherit");
  @include flex($direction: column, $gap: 1rem);
  padding-top: 3rem;

  @include larger-than($md) {
    padding-block: 2rem;
    @include flex(
      $direction: row,
      $justify: center,
      $align: center,
      $gap: 1rem
    );
  }
  
  @include larger-than($lg1) {
    padding-block: 0;
    gap: 2.5rem;
    // outline: 1px solid red;
  }
}

.left-section {
  .heading {
    font-weight: 600;
    font-size: clamp(rem(32px), 5vw, rem(50px));
    line-height: 1.3;
    text-align: center;
    max-width: 20ch;
    margin-inline: auto;

    @include larger-than($md) {
      text-align: left;
    }
    @include larger-than($lg1) {
      max-width: 15ch;
      font-size: clamp(rem(50px), 3.5vw, rem(60px));
    }

    span {
      color: var(--mantine-color-accent-5);
    }
  }

  .description {
    margin-top: 1rem;
    text-align: center;
    max-width: 35ch;
    margin-inline: auto;

    @include larger-than($md) {
      text-align: left;
      max-width: 45ch;
      margin-inline: 0;
    }
  }

  .download-wrapper {
    @include flex(row, center, center, 1rem);
    flex-wrap: wrap;
    margin-top: 2rem;

    @include larger-than($md) {
      justify-content: start;
    }

    .image {
      width: rem(150px);
    }
  }

  .avatar-wrapper {
    @include flex(column-reverse, center, center, 0.25rem);
    margin-top: 2rem;
    text-align: center;

    @include larger-than($md) {
      @include flex(row, start, start, 1.25rem);
    }

    .text {
      @include flex($direction: column, $gap: 0);

      @include larger-than($md) {
        align-items: start;
      }

      p:first-child {
        font-weight: 600;
        font-size: rem(25px);
        line-height: 1;
      }
      p:last-child {
        font-size: rem(14px);
        color: var(--mantine-color-dark-2);
      }
    }
  }
}

.right-section {
  z-index: 1;

  @include larger-than($md) {
    flex-basis: 50%;
    min-width: rem(450px);
  }

  .image-stack {
    position: relative;
    width: min(100%, rem(550px));
    height: 100%;
    margin-inline: auto;
    aspect-ratio: 1;
    z-index: 9;
  }

  .image-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: min(100%, rem(550px));
    transition: opacity 1s ease;
    // outline: 1px solid red;
  }
}
