import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./hero.module.scss";
import AppStoreImage from "~/assets/misc/app-store.png";
import PlaystoreImage from "~/assets/misc/playstore.png";
import WhatsappImage from "~/assets/misc/whatsapp.png";
import { Avatar, Image } from "@mantine/core";
import { IconPlus } from "@tabler/icons-react";

import Hero1EN from "~/assets/hero/1-en.png";
import Hero2EN from "~/assets/hero/2-en.png";
import Hero3<PERSON> from "~/assets/hero/3-en.png";
import Hero4<PERSON> from "~/assets/hero/4-en.png";
import { useEffect, useState } from "react";

const images = [Hero1EN, Hero2EN, Hero3EN, Hero4EN];

export const Hero = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <div className={classes["flex-content"]}>
          <div className={classes["left-section"]}>
            <h1 className={classes.heading}>
              Connect <br/>to <span>Doctors, Specialists, and At-Home</span> Services
            </h1>
            <p className={classes.description}>
              Say goodbye to long trips for doctor or specialist consultations and lab tests.
            </p>

            <div className={classes["download-wrapper"]}>
              <Image
                className={classes.image}
                src={AppStoreImage}
                alt="Download Kiko from our mobile app"
              />
              <Image
                className={classes.image}
                src={PlaystoreImage}
                alt="Download Kiko from our mobile app"
              />
              <Image
                className={classes.image}
                src={WhatsappImage}
                alt="Download Kiko from our mobile app"
              />
            </div>

            <div className={classes["avatar-wrapper"]}>
              <Avatar.Group spacing={18}>
                {Array.from({ length: 5 }).map((_, index) => (
                  <Avatar
                    key={index}
                    src={`https://i.pravatar.cc/50?img=${index + 1}`}
                    bd={"3px solid white"}
                  />
                ))}
                <Avatar>
                  <IconPlus size={18} stroke={2} />
                </Avatar>
              </Avatar.Group>

              <div className={classes.text}>
                <p>150K+</p>
                <p>satisfied users</p>
              </div>
            </div>
          </div>

          <div className={classes["right-section"]}>
            <div className={classes["image-stack"]}>
              {images.map((image, index) => (
                <div
                  key={index}
                  className={classes["image-wrapper"]}
                  style={{
                    opacity: currentIndex === index ? 1 : 0,
                  }}>
                  <img src={image} alt="" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
