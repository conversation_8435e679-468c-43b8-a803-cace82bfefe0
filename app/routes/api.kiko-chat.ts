import { json } from "@remix-run/node";
import type { ActionFunction, LoaderFunctionArgs } from "@remix-run/node";
import axios from "axios";
import { chatApi } from "~/utils/api.kiko.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const sessionId = url.searchParams.get("session_id");

  if (!sessionId) {
    return json({ error: "Missing session_id" }, { status: 400 });
  }

  // console.log("Calling endpoint: ", `/conversation/${sessionId}`)

  try {
    console.log(`/conversation/${sessionId}?medium=website`)

    const response = await axios.get(
      `${process.env.KIKO_CHAT_BASE_URL}/conversation/${sessionId}?medium=website` as string
    );

    return json({ source: "loader", data: response.data });
  } catch (error: any) {
    console.error("Kiko chat loader error:", error);

    // Handle different types of errors
    let errorMessage = "API call failed";
    let statusCode = 500;

    if (error?.response) {
      statusCode = error.response.status;

      // If the response data is HTML (like Heroku error pages), provide a generic message
      if (typeof error.response.data === 'string' && error.response.data.includes('<!DOCTYPE html>')) {
        errorMessage = `Kiko chat service is temporarily unavailable (${statusCode})`;
      } else if (error.response.data && typeof error.response.data === 'object') {
        errorMessage = error.response.data.message || error.response.data.error || errorMessage;
      } else if (typeof error.response.data === 'string') {
        errorMessage = error.response.data;
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return json(
      { error: errorMessage, source: "loader" },
      { status: statusCode }
    );
  }
}

export const action: ActionFunction = async ({ request }) => {
  // console.log("✅ /api/kiko-chat hit with method:", request.method);
  // console.log("✅ Endpoint:", `/chat`);

  const formData = await request.formData();
  const actionType = formData.get("action");
  const session_id = formData.get("session_id");
  const text_message = formData.get("text_message");

  const body: Record<string, any> = {
    medium: "website",
    message: {
      text_message: text_message,
    },
  };

  if (session_id) {
    body["session_id"] = session_id;
  }

  if (actionType === "send") {
    try {
      const response = await chatApi.post(
        `/chat/v2` as string,
        body
      );

      return json({ source: "action", data: response.data });
    } catch (error: any) {
      console.error("Kiko chat action error:", error);

      // Handle different types of errors
      let errorMessage = "API call failed";
      let statusCode = 500;

      if (error?.response) {
        statusCode = error.response.status;

        // If the response data is HTML (like Heroku error pages), provide a generic message
        if (typeof error.response.data === 'string' && error.response.data.includes('<!DOCTYPE html>')) {
          errorMessage = `Kiko chat service is temporarily unavailable (${statusCode})`;
        } else if (error.response.data && typeof error.response.data === 'object') {
          errorMessage = error.response.data.message || error.response.data.error || errorMessage;
        } else if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }

      return json(
        { error: errorMessage, source: "action" },
        { status: statusCode }
      );
    }
  }

  return json({ error: "Invalid action" }, { status: 400 });
};
