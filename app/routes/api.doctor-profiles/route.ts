import { json, LoaderFunctionArgs } from "@remix-run/node";
import { isAxiosError } from "axios";

import { DEFAULT_PAGINATION } from "~/constants";
import { DoctorProfile } from "~/types";
import { api } from "~/utils/api.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const serviceType = url.searchParams.get("serviceType") ?? "";
  const specialty = url.searchParams.get("specialty") ?? "";
  const search = url.searchParams.get("search") ?? "";
  //   console.log(serviceType, specialty);

  try {
    const res = await api.get("profile", {
      params: {
        ...(serviceType && { service_id: serviceType }),
        ...(specialty && { specialty }),
        ...(search && { search }),
        page: DEFAULT_PAGINATION.page,
        page_size: DEFAULT_PAGINATION.pageSize,
      },
    });
    // console.log(res);

    let data: DoctorProfile[] = [];

    if (res?.data.data.length) {
      // console.log(res.data.data);
      data = res?.data.data.map(
        ({
          profile_image,
          brief_description,
          location,
          years_of_experience,
          _id,
          license_number,
          user_info,
          specialties,
          services,
          slug,
          user_id,
          consultation_modes,
          specialization,
          languages,
        }: any) => ({
          id: _id,
          userId: user_id,
          name: user_info?.full_name,
          image: profile_image,
          location,
          briefDescription: brief_description,
          yearsOfExperience: years_of_experience,
          licenseNumber: license_number,
          specialties,
          services,
          slug,
          consultationModes: consultation_modes,
          specialization,
          languages,
        })
      );
    }

    return json({
      ok: true,
      data,
      error: null,
    });
  } catch (error) {
    console.error("[Error fetching doctor profiles]", error);

    // return json(
    //   {
    //     ok: false,
    //     data: null,
    //     error: {
    //       message: "There was an error getting doctor profiles",
    //       status: 500,
    //     },
    //   },
    //   { status: 500 }
    // );

    // Check if it's a network error (no response received)
    if (isAxiosError(error)) {
      if (error.response) {
        // Axios response error (e.g., 404, 500)
        return json(
          {
            ok: false,
            data: null,
            error:
              error.response.data?.detail || "Error fetching doctor profiles",
          },
          { status: error.response.status }
        );
      } else if (error.request) {
        // Network error (request was made, but no response)
        return json(
          {
            ok: false,
            data: null,
            error:
              "Network error: No response from server. Please try again later.",
          },
          { status: 503 } // 503 Service Unavailable or 504 Gateway Timeout
        );
      }
    }

    // General error handler for non-Axios errors
    return json(
      {
        ok: false,
        data: null,
        error: "There was an error getting doctor profiles",
      },
      { status: 500 }
    );
  }
};
