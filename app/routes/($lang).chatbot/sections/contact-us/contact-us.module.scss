@use "~/styles/index" as *;

.section {
  padding-block: 3rem;
  margin-top: 5rem;
  margin-bottom: -7rem;
  background-color: var(--mantine-color-accent-0);

  @include larger-than($md) {
    padding-block: 4rem;
    margin-top: 10rem;
  }
}

.flex-content {
  @include flex($direction: column, $gap: 2rem);

  @include larger-than($sm) {
    flex-direction: row;
    max-width: rem(1000px);
    margin-inline: auto;

    & > div {
      flex-basis: 50%;
    }
  }
}

.left-section {
  @include larger-than($md) {
    padding-left: 3rem;
  }
  .description {
    margin-top: 1rem;
    max-width: 40ch;
  }
}

.right-section {
  .wrapper {
    width: min(100%, rem(400px));
    margin-inline: auto;
    flex-grow: 1;
  }
}
