import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./contact-us.module.scss";
import { Button, Group, Stack, Text, TextInput, Textarea } from "@mantine/core";
import {
  IconBrandWhatsapp,
  IconBrandWhatsappFilled,
  IconMail,
  IconMailFilled,
  IconPhone,
  IconPhoneFilled,
} from "@tabler/icons-react";
import { Link } from "@remix-run/react";
import { useTranslation } from "~/hooks";

export const ContactUs = () => {
  const { t } = useTranslation();

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <div className={classes["flex-content"]}>
          <div className={classes["left-section"]}>
            <h2 className={classes.heading}>{t("chatbot-contact-heading")}</h2>
            <p className={classes.description}>
              {t("chatbot-contact-description")}
            </p>

            <Stack mt={"1.5rem"}>
              <Group gap={13}>
                <IconMailFilled size={18} stroke={1.5} />
                <Link to="#">
                  <Text inherit><EMAIL></Text>
                </Link>
              </Group>
              <Group gap={10}>
                <IconBrandWhatsappFilled size={18} stroke={1.5} />
                <Link to="#">
                  <Text inherit>+225 05 04 783 682</Text>
                </Link>
              </Group>
              <Group gap={10}>
                <IconPhoneFilled size={18} stroke={1.5} />
                <Link to="#">
                  <Text inherit>+225 05 02 876 887</Text>
                </Link>
              </Group>
            </Stack>
          </div>
          <div className={classes["right-section"]}>
            <Stack className={classes.wrapper}>
              <TextInput label={t("form-name")} />
              <TextInput label={t("form-organization-name")} />
              <TextInput label={t("form-email-phone")} />
              <Textarea label={t("form-description")} rows={5} />
              <Button mt={"1rem"} size="md">
                {t("form-submit")}
              </Button>
            </Stack>
          </div>
        </div>
      </div>
    </div>
  );
};