import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./what-we-offer.module.scss";
import { useTranslation } from "~/hooks";
import Image1 from "~/assets/what-we-offer/1.png";
import Image2 from "~/assets/what-we-offer/2.png";
import Image3 from "~/assets/what-we-offer/3.png";

export const WhatWeOffer = () => {
  const { t } = useTranslation();

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <h2 className={classes.heading}>{t("chatbot-what-we-offer-heading")}</h2>

        <div className={classes["flex-content"]}>
          <div className={classes.group}>
            <div className={classes["group-image"]}>
              <img src={Image1} alt="" />
            </div>
            <div className={classes["group-text-content"]}>
              <h3 className={classes["group-title"]}>
                {t("chatbot-offer-1-title")}
              </h3>
              <p className={classes["group-description"]}>
                {t("chatbot-offer-1-description")}
              </p>
            </div>
          </div>
          <div className={classes.group}>
            <div className={classes["group-image"]}>
              <img src={Image2} alt="" />
            </div>
            <div className={classes["group-text-content"]}>
              <h3 className={classes["group-title"]}>
                {t("chatbot-offer-2-title")}
              </h3>
              <p className={classes["group-description"]}>
                {t("chatbot-offer-2-description")}
              </p>
            </div>
          </div>
          <div className={classes.group}>
            <div className={classes["group-image"]}>
              <img src={Image3} alt="" />
            </div>
            <div className={classes["group-text-content"]}>
              <h3 className={classes["group-title"]}>
                {t("chatbot-offer-3-title")}
              </h3>
              <p className={classes["group-description"]}>
                {t("chatbot-offer-3-description")}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};