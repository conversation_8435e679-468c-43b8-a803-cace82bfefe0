@use "~/styles/index" as *;

.section {
  margin-top: 5rem;

  @include larger-than($md) {
    margin-top: 10rem;
  }
}

.heading {
  text-align: center;
}

.flex-content {
  @include flex($direction: column, $gap: 3rem);

  @include larger-than($sm) {
    gap: 5rem;
  }
}

.group {
  @include flex($direction: column-reverse, $gap: 2.5rem);

  @include larger-than($sm) {
    gap: 3rem;
  }

  @include larger-than($md) {
    @include flex($direction: row, $align: center, $gap: 4rem);
    max-width: rem(1000px);
    margin-inline: auto;
  }

  &:first-child {
    margin-top: 2rem;

    @include larger-than($md) {
      margin-top: 4rem;
    }
  }

  &:nth-child(2) {
    .group-image {
      max-width: rem(300px);
    }

    @include larger-than($md) {
      flex-direction: row-reverse;
    }
  }
  &:nth-child(3) {
    @include larger-than($md) {
      margin-top: 5rem;
    }
  }

  &-text-content {
    text-align: center;

    @include larger-than($md) {
      text-align: left;
    }
  }

  &-image {
    width: min(100%, rem(400px));
    margin-inline: auto;
  }

  &-title {
    font-weight: 600;
    font-size: clamp(1.5rem, 1.368rem + 0.5634vw, 1.875rem);
    max-width: 30ch;
    margin-inline: auto;

    @include larger-than($md) {
      margin-inline: 0;
      max-width: 25ch;
    }
  }

  &-description {
    margin-top: 1rem;
    max-width: 50ch;
    margin-inline: auto;

    @include larger-than($md) {
      margin-inline: 0;
      max-width: 40ch;
    }

    span {
      color: var(--mantine-color-accent-6);
      font-weight: 600;
    }
  }
}
