import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./hero.module.scss";
import { Avatar, Button, Image } from "@mantine/core";
import { useTranslation } from "~/hooks";

import HeroImage from "~/assets/hero/chatbot.png";
import { AI_AGENT_FOR_YOUR_BUSINESS_URL } from "~/constants";

export const Hero = () => {
  const { t } = useTranslation();

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <div className={classes["flex-content"]}>
          <div className={classes["left-section"]}>
            <h1 className={classes.heading}>
              {t("chatbot-hero-heading")
                .split("{{highlight}}")
                .map((part, index) => {
                  if (index === 1) {
                    // This is the highlighted part
                    const [highlightText, afterText] = part.split("{{/highlight}}");
                    return (
                      <span key={index}>
                        <span>{highlightText}</span>
                        {afterText}
                      </span>
                    );
                  }
                  return <span key={index}>{part}</span>;
                })}
            </h1>
            <p className={classes.description}>
              {t("chatbot-hero-description")}
            </p>

            <Button component="a" href={AI_AGENT_FOR_YOUR_BUSINESS_URL} target="_blank" size="md" className={classes.cta}>
              {t("contactUs")}
            </Button>
          </div>

          <div className={classes["right-section"]}>
            <div className={classes["image-wrapper"]}>
              <img src={HeroImage} alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
