@use "~/styles/index" as *;

.section {
  position: relative;
  @include size("100%", "100vh");
  margin-top: 2rem;
}

.flex-content {
  @include size("inherit");
  @include flex($direction: column, $gap: 2rem);
  padding-top: 3rem;

  @include larger-than($md) {
    padding-block: 2rem;
    @include flex(
      $direction: row,
      $justify: center,
      $align: center,
      $gap: 1rem
    );
  }
  @include larger-than($lg1) {
    padding-block: 0;
    gap: 2.5rem;
    // outline: 1px solid red;
  }
}

.left-section {
  @include flex($direction: column, $gap: 0);
  .heading {
    font-weight: 600;
    font-size: clamp(rem(32px), 5vw, rem(50px));
    line-height: 1.3;
    text-align: center;
    max-width: 20ch;
    margin-inline: auto;

    @include larger-than($md) {
      text-align: left;
    }
    @include larger-than($lg1) {
      max-width: 15ch;
      font-size: clamp(rem(50px), 3.5vw, rem(60px));
    }

    span {
      color: var(--mantine-color-accent-5);
    }
  }

  .description {
    margin-top: 1rem;
    text-align: center;
    max-width: 35ch;
    margin-inline: auto;

    @include larger-than($md) {
      text-align: left;
      max-width: 45ch;
      margin-inline: 0;
    }
  }

  .cta {
    margin-top: 2rem;
    margin-inline: auto;
    width: fit-content;

    @include larger-than($md) {
      margin-inline: 0;
    }
  }
}

.right-section {
  z-index: 1;

  @include larger-than($md) {
    flex-basis: 50%;
    min-width: rem(450px);
  }

  .image-wrapper {
    @include mx-auto-width(min(100%, rem(450px)));
  }
}
