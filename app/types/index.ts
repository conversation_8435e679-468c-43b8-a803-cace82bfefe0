import { ComboboxItem } from "@mantine/core";

import { AppointmentCategory } from "./appointment";

export interface DoctorProfile {
  id: string;
  userId: string;
  name: string;
  image?: string;
  briefDescription?: string;
  location?: string;
  yearsOfExperience: number;
  licenseNumber: string;
  specialties?: string[];
  services?: string[];
  slug: string;
  consultationModes?: AppointmentCategory[];
  specialization: string;
  languages: string[];
}

export interface Pagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

export interface ComboboxItemPrice extends ComboboxItem {
  price?: number;
  duration?: number;
  category?: string;
}