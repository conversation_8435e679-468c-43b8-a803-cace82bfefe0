interface BasicUser {
    id: string;
    fullName: string;
  }
  
  interface PatientInfo {
    firstName: string;
    middleName?: string;
    surname: string;
    dob: Date;
    gender: string;
    phoneNumber: string;
    email?: string;
  }
  
  export type AppointmentStatus = "pending" | "cancelled" | "rejected" | "booked";
  export type AppointmentCategory = "teleconsultation" | "homecare";
  
  interface AppointmentDetail extends PatientInfo {
    serviceDate: Date; // Will be deprecated soon
    serviceType: string;
    serviceTypeId?: string;
    paymentMethod?: string;
    city?: string;
    cost?: number;
    additionalHealthInfo?: string;
    hasInsurance?: boolean;
    insuranceType?: string;
    insuranceInfo?: string;
  }
  
  export interface TeleconsultationAppointmentDetail extends AppointmentDetail {
    callTime?: number;
    callStatus?: "not_started" | "started" | "complete";
  }
  
  export interface HomeCareDetail extends AppointmentDetail {
    zone: string;
    neighborhood?: string;
    exigences?: string;
  }
  
  interface PaymentTransaction {
    paymentMethod?: string;
    amountPaid: number;
    receivedBy?: string;
    date: Date;
    paidAt?: Date;
    reference?: string;
  }
  
  export interface Appointment {
    id: string;
    details: TeleconsultationAppointmentDetail | HomeCareDetail;
    category: AppointmentCategory;
    status: AppointmentStatus;
    department: string;
    language?: string;
    doctorAssigned?: BasicUser | string;
    isFollowUp?: boolean;
    createdAt: Date;
    updatedAt: Date;
    paymentTransaction?: PaymentTransaction;
    appointmentDate: Date;
  }
  
  export interface AppointmentSlot {
    id: string;
    startTime: Date;
    endTime: Date;
    quantity: number;
    doctor: string;
  }