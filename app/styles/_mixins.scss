// -----------------------------------------------------------------------------
// This file contains all application-wide Sass mixins.
// -----------------------------------------------------------------------------
@use "./variables" as *;
@use "./functions" as *;
@use "sass:list" as *;

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex(
  $direction: row,
  $justify: flex-start,
  $align: stretch,
  $gap: null
) {
  @if not index($list: $justify-content-values, $value: $justify) {
    @error "Invalid justify-content value: #{$justify}";
  }
  @if not index($list: $align-items-values, $value: $align) {
    @error "Invalid align-items value: #{$align}";
  }

  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  gap: $gap;
}

// Initial setup for pseudo elements
@mixin pseudo {
  content: "";
  position: absolute;
}

@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

@mixin circle($diameter) {
  border-radius: 50%;
  @include size($diameter);
}

@mixin line-clamp($lines: 1) {
  @if $lines <= 0 {
    @error "No. of lines has to be greater than 0: #{$lines}";
  }

  display: -webkit-box;
  -webkit-line-clamp: $lines;
  overflow: hidden;
  -webkit-box-orient: vertical;
}

@mixin clip-border($border-radius: 0px) {
  border-radius: $border-radius;
  overflow: hidden;
}

@mixin mx-auto-width($width: null) {
  margin-inline: auto;
  width: if($width == null, fit-content, $width);
}

// Media queries
@mixin smaller-than($breakpoint) {
  @if not is-valid-size($breakpoint) {
    @error "Invalid size passed to smaller-than mixin: #{$breakpoint}. Must be a positive number.";
  }

  // Subtract 0.1px to avoid intersection with larger-than mixin
  $new-breakpoint: $breakpoint - em(0.1px);

  @media screen and (max-width: $new-breakpoint) {
    @content;
  }
}

@mixin larger-than($breakpoint) {
  @if not is-valid-size($breakpoint) {
    @error "Invalid size passed to larger-than mixin: #{$breakpoint}. Must be a positive number.";
  }

  @media screen and (min-width: $breakpoint) {
    @content;
  }
}
