@use "./functions" as *;
@use "./mixins" as *;

:root {
  font-synthesis: weight style;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  // CSS variables here ----------------------------------------

  --font-primary: "Poppins", sans-serif;
  --font-secondary: "Noto Sans Mono", monospace;

  --ease-slow-start: cubic-bezier(0.52, 0.01, 0, 1);
  --ease-quick-start-and-end: cubic-bezier(0.19, 1, 0.22, 1);
  --ease-almost-linear: cubic-bezier(0.4, 0.6, 0.6, 1);
  --ease-text-reveal: cubic-bezier(0.77, 0, 0.175, 1);

  // APP-SPECIFIC CSS variables here ----------------------------------------
}

*,
::before,
::after {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: inherit;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  min-height: 100%;
  background-color: var(--clr-white);
  font-family: var(--font-primary);
  font-size: rem(16px);
  line-height: 1.5;
  overflow-x: hidden;
}

h2 {
  line-height: 1.3;
  font-size: clamp(2rem, 1.6479rem + 1.5023vw, 3rem);
  font-weight: 600;
}

img {
  vertical-align: middle;
  @include size(100%);
  max-width: 100%;
  font-style: italic; // Distinguish alt text
  object-fit: cover;
  object-position: center;
  border-style: none; // Remove the border on images inside links in IE 10.

  // To be used in conjunction with low-res placeholder images as backgrounds
  /* background-repeat: no-repeat; */
  /* background-size: cover; */
}

a {
  font-family: inherit;
  color: inherit;
  display: inline-block;
  text-decoration: none;
}

button,
input,
select,
textarea {
  border: none;
  font-family: inherit;
}

button {
  cursor: pointer;
  background: none;
  white-space: nowrap;
}

//  Correct the inability to style clickable types in iOS and Safari.

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

// Mantine styles -----------------------------------------------------------
.mantine-button {
  font-weight: 500;
}

// Remove all animations and transitions for users
// who have it turned off in system settings
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-delay: -1ms !important;
    animation-duration: 1ms !important;
    animation-iteration-count: 1 !important;
    background-attachment: initial !important;
    scroll-behavior: auto !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
  }
}
