/**
 * Normalizes a string for search comparison by:
 * 1. Converting to lowercase
 * 2. Decomposing accented letters (NFD normalization)
 * 3. Removing diacritical marks (accents)
 * 
 * This allows searching for French text without proper accents.
 * For example: "médecin" and "medecin" will both normalize to "medecin"
 */
export const normalizeString = (str: string): string =>
  str
    .toLocaleLowerCase()
    .normalize("NFD") // decompose accented letters
    .replace(/[\u0300-\u036f]/g, ""); // remove diacritical marks
