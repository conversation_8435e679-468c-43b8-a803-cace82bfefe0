const SESSION_KEY = 'KIKO_WEBCHAT';

interface SessionData {
  value: string;
  expiresAt: number; // in milliseconds (timestamp)
}

/**
 * Stores the chatSessionId with an optional expiration time (in minutes).
 * @param sessionId - The session ID string to be saved.
 * @param expiresInMinutes - Optional expiration time in minutes (default: 30).
 */
export function setChatSessionId(sessionId: string, expiresInMinutes = 30): void {
  if (typeof window !== 'undefined') {
    const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;
    const data: SessionData = { value: sessionId, expiresAt };
    localStorage.setItem(SESSION_KEY, JSON.stringify(data));
  }
}

/**
 * Retrieves the chatSessionId from local storage, checking for expiration.
 * @returns The session ID string if valid and not expired, otherwise null.
 */
export function getChatSessionId(): string | null {
  if (typeof window === 'undefined') return null;

  const raw = localStorage.getItem(SESSION_KEY);
  if (!raw) return null;

  try {
    const data: SessionData = JSON.parse(raw);
    // if (Date.now() > data.expiresAt) {
    //   clearChatSessionId(); // Clean up if expired
    //   return null;
    // }
    return data.value;
  } catch {
    clearChatSessionId(); // If malformed, also clear
    return null;
  }
}

/**
 * Deletes the chatSessionId from local storage.
 */
export function clearChatSessionId(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(SESSION_KEY);
  }
}
