import axios from "axios";

const baseURL = process.env.KIKO_CHAT_BASE_URL;

export const chatApi = axios.create({
  baseURL: `${baseURL}`,
  headers: {
    "X-API-Key": process.env.X_API_KEY,
    "Content-Type": "application/json"
  }
});

// Add request interceptor to log all requests
chatApi.interceptors.request.use(
  (config) => {
    console.log('Request config:', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      data: config.data
    });
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor to handle HTML error responses
chatApi.interceptors.response.use(
  (response) => {
    // Check if response is HTML when we expect JSON
    if (typeof response.data === 'string' && response.data.includes('<!DOCTYPE html>')) {
      console.error('Received HTML response instead of JSON:', response.data);
      throw new Error(`Server returned HTML error page (${response.status})`);
    }
    return response;
  },
  (error) => {
    console.error('Response error:', error);

    // Handle HTML error responses in error cases too
    if (error.response && typeof error.response.data === 'string' && error.response.data.includes('<!DOCTYPE html>')) {
      error.response.data = {
        error: `Kiko chat service is temporarily unavailable (${error.response.status})`,
        message: 'Service returned an error page'
      };
    }

    return Promise.reject(error);
  }
);
