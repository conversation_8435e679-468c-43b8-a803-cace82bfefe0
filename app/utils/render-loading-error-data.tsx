interface Props {
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  dataLength: number; // Either "array.length" or "Object.keys(data).length"
  emptyDataComponent?: React.ReactNode;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  dataComponent?: React.ReactNode;
}
export const renderLoadingErrorData = ({
  isLoading,
  isError,
  isSuccess,
  dataLength,
  emptyDataComponent,
  loadingComponent,
  errorComponent,
  dataComponent,
}: Props) => {
  // console.log({
  //   isError: isError,
  //   isLoading: isLoading,
  //   isSuccess: isSuccess,
  //   dataLength: dataLength,
  // });
  if (isError) {
    // else if (isError && !dataLength) {
    return errorComponent;
    // } else if (isLoading && !dataLength) {
  } else if (isLoading) {
    return loadingComponent;
  } else if (isSuccess && dataLength) return dataComponent;
  else if (isSuccess && !dataLength) {
    return emptyDataComponent;
    // } else return dataComponent;
  }
};
