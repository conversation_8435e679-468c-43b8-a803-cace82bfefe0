export function abbreviateMiddleNames(fullName: string): string {
  // Split the name into words, remove leading/trailing spaces and split into words
  const nameParts = fullName.trim().split(" ").filter(Boolean); // Filter removes empty parts

  // Check if the name already includes "<PERSON>."
  const hasTitle = nameParts[0].toLowerCase() === "dr.";
  const mainParts = hasTitle ? nameParts.slice(1) : nameParts;

  // If there's only one or two main parts, return the name as is, preserving "Dr." if present
  if (mainParts.length <= 2) {
    return hasTitle ? `Dr. ${mainParts.join(" ")}` : mainParts.join(" ");
  }

  // Extract the first and last name
  const firstName = mainParts[0];
  const lastName = mainParts[mainParts.length - 1];

  // Abbreviate all middle names
  const middleNames = mainParts.slice(1, -1);
  const abbreviatedMiddleNames = middleNames.length
    ? middleNames.map(name => `${name[0]}.`).join(" ")
    : ""; // Avoid "undefined."

  // Combine the title (if present), first name, abbreviated middle names, and last name
  const finalName = hasTitle
    ? `Dr. ${firstName} ${abbreviatedMiddleNames} ${lastName}`
    : `${firstName} ${abbreviatedMiddleNames} ${lastName}`;

  // console.log("final name:", finalName);
  return finalName;
}
