import React, { useEffect, useState } from "react";
import Markdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Prism as SyntaxHighlighterBase } from "react-syntax-highlighter";
import type { Components } from "react-markdown";
import classes from "./markdown-renderer.module.scss";

const SyntaxHighlighter = SyntaxHighlighterBase as any;

interface MarkdownRendererProps {
  markdown: string;
  className?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ markdown, className = "" }) => {
  const [dracula, setDracula] = useState<Record<string, React.CSSProperties> | null>(null);

  useEffect(() => {
    // Dynamically import theme on client
    import("react-syntax-highlighter/dist/esm/styles/prism/dracula").then((mod) => {
      setDracula(mod.default);
    });
  }, []);

  const components: Components = {
    code: (props: any) => {
      const { inline, className: codeClass, children, ...rest } = props;
      const match = /language-(\w+)/.exec(codeClass ?? "");

      if (!inline && match && dracula) {
        return (
          <SyntaxHighlighter
            style={dracula}
            language={match[1]}
            PreTag="div"
            wrapLongLines
            {...rest}
          >
            {String(children).replace(/\n$/, "")}
          </SyntaxHighlighter>
        );
      }

      return (
        <code className={classes["inline-code"]} {...rest}>
          {children}
        </code>
      );
    },

    a: (props: any) => {
      const { href, children, ...rest } = props;
      return (
        <a
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          className={classes.link}
          {...rest}
        >
          {children}
        </a>
      );
    },

    img: (props: any) => <img {...props} className={classes.image} />,
  };

  return (
    <div className={`${classes.wrapper} ${className}`}>
      <Markdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]} components={components}>
        {markdown}
      </Markdown>
    </div>
  );
};

export default MarkdownRenderer;
