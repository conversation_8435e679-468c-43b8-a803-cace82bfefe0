@use "~/styles/index" as *;

.wrapper {
  font-size: 1rem;
  line-height: 1.6;
  word-break: break-word;
  overflow-x: auto;
}

/* List indentation respects container padding */
.wrapper ul,
.wrapper ol {
  margin: 0 0 1rem 0;
  padding-left: 1.25rem;
}

/* Code block base (Prism still handles token colours) */
.wrapper pre {
  margin: 0 0 1rem 0;
  overflow: auto;
  background: #0d1117;
  border-radius: 6px;
  padding: 1rem;
}

.inline-code {
  background: #f5f5f5;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
}

.link {
  color: #1e63d9;
  text-decoration: underline;
  word-break: break-all;

  &:hover {
    text-decoration: none;
  }
}

.image {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0.5rem auto;
}