"use client";

import utilityClasses from "~/styles/utilities.module.scss";
import Logo from "~/assets/misc/logo.png";
import { Button, Group, Image, Select } from "@mantine/core";
import { Link, useLocation, useNavigate, useParams } from "@remix-run/react";
import i18nConfig from "~/localization/i18n";
import classes from "./navbar.module.scss";
import { useTranslation } from "~/hooks";
import LogoIcon from "~/assets/misc/logo-icon.svg?react";
import { useMediaQuery } from "@mantine/hooks";

import FlagEN from "~/assets/icons/gb.svg";
import FlagFR from "~/assets/icons/fr.svg";
import { APPOINTMENT_BOOKING_WEBSITE_URL } from "~/constants";

export const Navbar = () => {
  const { lang } = useParams();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const largerThanSm = useMediaQuery("(min-width: 640px)");

  const selectedLang = i18n.language?.toLowerCase() ?? "en";
  const currentFlag = selectedLang === "fr" ? FlagFR : FlagEN;

  const handleLanguageChange = (value: string | null) => {
    if (!value) return;
    const newLang = value.toLowerCase();

    if (i18nConfig.supportedLngs.includes(newLang)) {
      i18n.changeLanguage(newLang);

      // Preserve the current route when switching languages
      const currentPath = location.pathname;
      const pathSegments = currentPath.split('/').filter(Boolean);

      // Remove the current language from the path if it exists
      if (pathSegments.length > 0 && i18nConfig.supportedLngs.includes(pathSegments[0])) {
        pathSegments.shift();
      }

      // Construct the new path with the new language
      const newPath = `/${newLang}${pathSegments.length > 0 ? '/' + pathSegments.join('/') : ''}`;
      navigate(newPath);
    }
  };

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <nav className={classes.nav}>
          <Group justify="space-between" gap={"5"}>
            <Link
              to={`/${lang && i18nConfig.supportedLngs.includes(lang) ? lang : ""
                }`}
              className={classes.logo}
            >
              <LogoIcon title="La Ruche Health" className={classes["logo-svg"]} />
              <Image
                w={150}
                src={Logo}
                alt="La Ruche Health"
                className={classes["logo-img"]}
              />
            </Link>

            <Group className={classes["nav-right"]}>
              <Button
                component="a"
                href={APPOINTMENT_BOOKING_WEBSITE_URL}
                target="_blank"
                rel="noopener noreferrer"
                color="accent"
                radius="xl"
                size={largerThanSm ? "sm" : "xs"}
              >
                {t("bookAppointment")}
              </Button>

              {
                largerThanSm ?
                  <Select
                    data={["FR", "EN"]}
                    value={selectedLang.toUpperCase()}
                    onChange={handleLanguageChange}
                    leftSection={
                      <div
                        style={{ display: "flex", alignItems: "center", gap: "0.1rem" }}
                      >
                        <img
                          src={currentFlag}
                          alt={`${selectedLang} flag`}
                          style={{ width: "1.5rem", height: "1.2rem" }}
                        />

                        {/* <div>{selectedLang}</div> */}
                      </div>
                    }
                    maw={largerThanSm ? 90 : 70}
                    size={largerThanSm ? "sm" : "xs"}
                  /> :
                  <Select
                    data={["FR", "EN"]}
                    value={selectedLang.toUpperCase()}
                    onChange={handleLanguageChange}
                    maw={largerThanSm ? 90 : 70}
                    size={largerThanSm ? "sm" : "xs"}
                  />
              }
            </Group>
          </Group>
        </nav>
      </div>
    </div>
  );
};
