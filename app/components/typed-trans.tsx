import { Trans as OriginalTrans } from "react-i18next";
import { TranslationKey } from "~/hooks/use-typed-translation";

/**
 * A type-safe wrapper component for `react-i18next`'s `Trans` component.
 * Ensures that the `i18nKey` prop is a valid key from the English translation
 * file, providing compile-time safety for translation keys.
 *
 * @param i18nKey - The key for the translation string in the English translations file.
 * @param props - Additional props to be passed to the original `Trans` component.
 * @returns A `Trans` component configured with type-safe translation keys.
 */
//
export const Trans = ({
  i18nKey,
  ...props
}: Omit<React.ComponentProps<typeof OriginalTrans>, "i18nKey"> & {
  i18nKey: TranslationKey;
}) => {
  return <OriginalTrans i18nKey={i18nKey} {...props} />;
};
