@use "~/styles/index" as *;

.section {
  margin-top: 7rem;
  background-color: #f8f5ff;
  padding-block: 3rem;
}

.icon-wrapper {
  @include flex-center;
  border: 1px solid var(--mantine-color-black);
  @include circle(rem(35px));

  svg {
    @include size(rem(20px));
  }
}

.download-wrapper-container {
  @supports (container-type: inline-size) {
    container-type: inline-size;
  }
}

.download-wrapper {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;

  .image {
    width: rem(150px);
  }

  // Forcing Sass interpolation using #{}
  @container (min-width: #{rem(470px)}) {
    flex-direction: row;
  }
}