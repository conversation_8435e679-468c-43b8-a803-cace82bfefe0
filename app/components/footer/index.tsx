"use client";

import { Divider, Grid, Group, Image, Stack, Text } from "@mantine/core";
import utilityClasses from "~/styles/utilities.module.scss";
import Logo from "~/assets/misc/logo.png";
import classes from "./footer.module.scss";
import {
  IconBrandFacebook,
  IconBrandInstagram,
  IconBrandLinkedin,
  IconBrandTwitter,
  IconBrandWhatsapp,
  IconBrandX,
  IconBrandYoutube,
  IconMailFilled,
  IconMapPinFilled,
  IconPhoneFilled,
} from "@tabler/icons-react";
import { Link } from "@remix-run/react";
import AppStoreEN from "~/assets/misc/app-store-en.png";
import AppStoreFR from "~/assets/misc/app-store-fr.png";
import PlaystoreEN from "~/assets/misc/playstore-en.png";
import PlaystoreFR from "~/assets/misc/playstore-fr.png";
import WhatsappEN from "~/assets/misc/whatsapp-en.png";
import WhatsappFR from "~/assets/misc/whatsapp-fr.png";
import <PERSON><PERSON> from "~/assets/misc/messenger-en.png";
import MessengerFR from "~/assets/misc/messenger-fr.png";
import { useTranslation } from "~/hooks";
import { KIKO_MESSENGER_LINK, KIKO_WHATSAPP_LINK, LARUCHE_APPSTORE_LINK, LARUCHE_FACEBOOK_LINK, LARUCHE_INSTAGRAM_LINK, LARUCHE_LINKEDIN_LINK, LARUCHE_PLAYSTORE_LINK, LARUCHE_X_LINK, LARUCHE_YOUTUBE_LINK } from "~/constants";

export const Footer = () => {
  const { t, i18n } = useTranslation();

  const lang = i18n.language.startsWith("fr") ? "fr" : "en";

  const AppStoreImage = lang === "fr" ? AppStoreFR : AppStoreEN;
  const PlaystoreImage = lang === "fr" ? PlaystoreFR : PlaystoreEN;
  const WhatsappImage = lang === "fr" ? WhatsappFR : WhatsappEN;
  const MessengerImage = lang === "fr" ? MessengerFR : MessengerEN;


  return (
    <div className={classes.section}>
      <Stack className={utilityClasses.container} gap={"2rem"}>
        <Grid gutter={"3rem"}>
          <Grid.Col span={{ base: 12, md: 3.5 }}>
            <Stack gap="xs">
              <Image
                w={{ base: 185, md: 200 }}
                src={Logo}
                alt="La Ruche Health"
                ml={-5}
              />
              <Group gap={"xs"}>
                <a href={LARUCHE_FACEBOOK_LINK} className={classes["icon-wrapper"]} target="_blank" rel="noopener noreferrer">
                  <IconBrandFacebook />
                </a>
                <a href={LARUCHE_YOUTUBE_LINK} className={classes["icon-wrapper"]} target="_blank" rel="noopener noreferrer">
                  <IconBrandYoutube />
                </a>
                <a href={LARUCHE_LINKEDIN_LINK} className={classes["icon-wrapper"]} target="_blank" rel="noopener noreferrer">
                  <IconBrandLinkedin />
                </a>
                <a href={LARUCHE_X_LINK} className={classes["icon-wrapper"]} target="_blank" rel="noopener noreferrer">
                  <IconBrandX />
                </a>
                <a href={LARUCHE_INSTAGRAM_LINK} className={classes["icon-wrapper"]} target="_blank" rel="noopener noreferrer">
                  <IconBrandInstagram />
                </a>
                <a href={KIKO_WHATSAPP_LINK} className={classes["icon-wrapper"]} target="_blank" rel="noopener noreferrer">
                  <IconBrandWhatsapp />
                </a>
              </Group>
            </Stack>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: "auto" }}>
            <Group justify="center">
              <a href={LARUCHE_APPSTORE_LINK} target="_blank" rel="noopener noreferrer">
                <Image w={150} src={AppStoreImage} alt="" />
              </a>

              <a href={LARUCHE_PLAYSTORE_LINK} target="_blank" rel="noopener noreferrer">
                <Image w={150} src={PlaystoreImage} alt="" />
              </a>

              <a href={KIKO_WHATSAPP_LINK} target="_blank" rel="noopener noreferrer">
                <Image w={150} src={WhatsappImage} alt="" />
              </a>

              <a href={KIKO_MESSENGER_LINK} target="_blank" rel="noopener noreferrer">
                <Image w={150} src={MessengerImage} alt="" />
              </a>
            </Group>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 3.5 }}>
            <Text fz={15} fw={500}>
              {t("getInTouch")}
            </Text>
            <Stack mt={"0.5rem"} gap={"xs"}>
              <Link to="/">
                <Group gap={5}>
                  <IconMailFilled size={16} stroke={1.5} />
                  <Text fz={14}><EMAIL></Text>
                </Group>
              </Link>
              <Link to="/">
                <Group gap={5}>
                  <IconPhoneFilled size={16} stroke={1.5} />
                  <Text fz={14}>+225 05 02 87 6887</Text>
                </Group>
              </Link>
              <Link to="/">
                <Group gap={5} align="start">
                  <IconMapPinFilled size={16} stroke={1.5} />
                  <Stack gap={5}>
                    <Text fz={14}>Le Phare Coworking,</Text>
                    <Text fz={14}>Abidjan, Côte d’Ivoire</Text>
                  </Stack>
                </Group>
              </Link>
            </Stack>
          </Grid.Col>
        </Grid>
        <Divider color={"black"} />
        <Text ta={"center"} fz={14}>
          &copy; {new Date().getFullYear()} La Ruche Health Inc.{" "}
          {t("allRightsReserved")}
        </Text>
      </Stack>
    </div>
  );
};
