{"name": "la-ruche-landing-page", "version": "2025.1.0", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "tsc": "tsc", "preview": "dotenv -e .env.production.local -- npm run build && dotenv -e .env.production.local -- npm run start"}, "dependencies": {"@mantine/carousel": "^7.17.4", "@mantine/core": "^7.17.4", "@mantine/hooks": "^7.17.4", "@mantine/spotlight": "^7.17.4", "@remix-run/node": "^2.16.5", "@remix-run/react": "^2.16.5", "@remix-run/serve": "^2.16.5", "@tabler/icons-react": "^3.31.0", "axios": "^1.8.4", "embla-carousel-autoplay": "^7.1.0", "embla-carousel-react": "^7.1.0", "framer-motion": "^12.19.1", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^2.7.3", "isbot": "^4.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remix-i18next": "^6.4.1"}, "devDependencies": {"@remix-run/dev": "^2.16.5", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.21", "dotenv-cli": "^8.0.0", "eslint": "^8.57.1", "eslint-import-resolver-typescript": "^3.10.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "postcss": "^8.5.3", "sass": "^1.83.4", "typescript": "^5.1.6", "vite": "^5.4.18", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^4.3.2"}, "optionalDependencies": {"@esbuild/linux-x64": "0.25.2", "@rollup/rollup-linux-x64-gnu": "4.34.9"}, "engines": {"node": ">=20.0.0"}}